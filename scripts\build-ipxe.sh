#!/bin/bash
# ThinqAlpine iPXE Build Script
# Builds iPXE boot files with embedded ThinqAlpine script

set -euo pipefail

# Script arguments
ARCH="${1:-x86_64}"
IPXE_DIR="${2:-build/ipxe}"
OUTPUT_DIR="${3:-build/output}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Clone iPXE source
clone_ipxe() {
    local ipxe_src="$IPXE_DIR/src"
    
    log_info "Cloning iPXE source..."
    
    if [ -d "$ipxe_src" ]; then
        log_info "iPXE source already exists, updating..."
        cd "$ipxe_src"
        git pull
    else
        mkdir -p "$IPXE_DIR"
        git clone https://github.com/ipxe/ipxe.git "$ipxe_src"
        cd "$ipxe_src"
    fi
    
    # Checkout stable version
    git checkout v1.21.1
    
    log_success "iPXE source ready"
}

# Prepare embedded script
prepare_embedded_script() {
    local ipxe_src="$IPXE_DIR/src"
    local script_file="$ipxe_src/thinqalpine.ipxe"
    
    log_info "Preparing embedded iPXE script..."
    
    # Copy and customize the iPXE script
    cp "$PROJECT_ROOT/ipxe/thinqalpine.ipxe" "$script_file"
    
    # Replace variables in script
    sed -i "s/\${THINQALPINE_VERSION}/$THINQALPINE_VERSION/g" "$script_file"
    sed -i "s/\${BUILD_ID}/$BUILD_ID/g" "$script_file"
    
    log_success "Embedded script prepared: $script_file"
}

# Configure iPXE build
configure_ipxe_build() {
    local ipxe_src="$IPXE_DIR/src"
    
    log_info "Configuring iPXE build..."
    
    cd "$ipxe_src"
    
    # Create custom config header
    cat > config/local/general.h << 'EOF'
/* ThinqAlpine iPXE Configuration */

/* Enable additional protocols */
#define DOWNLOAD_PROTO_HTTPS
#define DOWNLOAD_PROTO_FTP
#define DOWNLOAD_PROTO_NFS

/* Enable additional commands */
#define IMAGE_SCRIPT
#define NSLOOKUP_CMD
#define PING_CMD
#define NVO_CMD
#define CONSOLE_CMD

/* Enable console types */
#define CONSOLE_SERIAL
#define CONSOLE_FRAMEBUFFER

/* Network configuration */
#define NET_PROTO_IPV6

/* Crypto support */
#define CRYPTO_80211_WEP
#define CRYPTO_80211_WPA
#define CRYPTO_80211_WPA2

/* Disable unnecessary features to save space */
#undef SANBOOT_PROTO_ISCSI
#undef SANBOOT_PROTO_AOE
#undef SANBOOT_PROTO_IB_SRP
#undef SANBOOT_PROTO_FCP

/* Custom branding */
#define PRODUCT_NAME "ThinqAlpine"
#define PRODUCT_SHORT_NAME "ThinqAlpine"
EOF

    log_success "iPXE build configured"
}

# Build iPXE for BIOS
build_ipxe_bios() {
    local ipxe_src="$IPXE_DIR/src"
    local output_file="$OUTPUT_DIR/undionly.kpxe"
    
    log_info "Building iPXE for BIOS (x86)..."
    
    cd "$ipxe_src"
    
    # Build BIOS version with embedded script
    make clean
    NO_WERROR=1 make bin-i386-pcbios/undionly.kpxe EMBED=thinqalpine.ipxe -j"$PARALLEL_JOBS"
    
    # Copy to output directory
    mkdir -p "$OUTPUT_DIR"
    cp bin-i386-pcbios/undionly.kpxe "$output_file"
    
    log_success "BIOS iPXE built: $output_file"
}

# Build iPXE for UEFI
build_ipxe_uefi() {
    local ipxe_src="$IPXE_DIR/src"
    local arch_target=""
    local output_file=""
    
    case "$ARCH" in
        x86_64)
            arch_target="bin-x86_64-efi"
            output_file="$OUTPUT_DIR/ipxe-x86_64.efi"
            ;;
        aarch64)
            arch_target="bin-arm64-efi"
            output_file="$OUTPUT_DIR/ipxe-aarch64.efi"
            ;;
        *)
            log_error "Unsupported architecture for UEFI: $ARCH"
            return 1
            ;;
    esac
    
    log_info "Building iPXE for UEFI ($ARCH)..."
    
    cd "$ipxe_src"
    
    # Build UEFI version with embedded script
    make clean
    NO_WERROR=1 make "$arch_target/ipxe.efi" EMBED=thinqalpine.ipxe -j"$PARALLEL_JOBS"
    
    # Copy to output directory
    mkdir -p "$OUTPUT_DIR"
    cp "$arch_target/ipxe.efi" "$output_file"
    
    log_success "UEFI iPXE built: $output_file"
}

# Create iPXE configuration files
create_ipxe_configs() {
    local config_dir="$OUTPUT_DIR/ipxe-configs"
    
    log_info "Creating iPXE configuration files..."
    
    mkdir -p "$config_dir"
    
    # Create example DHCP configuration
    cat > "$config_dir/dhcpd.conf.example" << 'EOF'
# Example DHCP configuration for ThinqAlpine
# Add this to your DHCP server configuration

# Global options
option space ipxe;
option ipxe-encap-opts code 175 = encapsulate ipxe;
option ipxe.priority code 1 = signed integer 8;
option ipxe.keep-san code 8 = unsigned integer 8;
option ipxe.skip-san-boot code 9 = unsigned integer 8;
option ipxe.syslogs code 85 = string;
option ipxe.cert code 91 = string;
option ipxe.privkey code 92 = string;
option ipxe.crosscert code 93 = string;
option ipxe.no-pxedhcp code 176 = unsigned integer 8;
option ipxe.bus-id code 177 = string;
option ipxe.san-filename code 188 = string;
option ipxe.bios-drive code 189 = unsigned integer 8;
option ipxe.username code 190 = string;
option ipxe.password code 191 = string;
option ipxe.reverse-username code 192 = string;
option ipxe.reverse-password code 193 = string;
option ipxe.version code 235 = string;

# Subnet configuration
subnet *********** netmask ************* {
    range ************0 *************;
    option routers ***********;
    option domain-name-servers *******, *******;
    
    # PXE boot configuration
    next-server ************;  # TFTP server IP
    
    # Boot filename based on client architecture
    if exists user-class and option user-class = "iPXE" {
        # iPXE already loaded, boot ThinqAlpine
        filename "http://************/thinq/boot.ipxe";
    } elsif option arch = 00:07 or option arch = 00:09 {
        # UEFI x86_64
        filename "ipxe-x86_64.efi";
    } elsif option arch = 00:0b {
        # UEFI aarch64
        filename "ipxe-aarch64.efi";
    } else {
        # BIOS
        filename "undionly.kpxe";
    }
    
    # ThinqAlpine specific options
    option thinq-server "http://************";
    option qemu_cmd "-m 4096 -smp 4";
}
EOF

    # Create example iPXE menu
    cat > "$config_dir/boot.ipxe.example" << 'EOF'
#!ipxe
# ThinqAlpine Boot Menu Example

:start
menu ThinqAlpine Boot Menu
item --gap --             ------------------------- ThinqAlpine Options -------------------------
item thinq-default        Boot ThinqAlpine (Default: 2GB RAM, 2 CPUs)
item thinq-4gb            Boot ThinqAlpine (4GB RAM, 4 CPUs)
item thinq-8gb            Boot ThinqAlpine (8GB RAM, 8 CPUs)
item thinq-custom         Boot ThinqAlpine (Custom Configuration)
item --gap --             ------------------------- Utilities -------------------------
item shell                iPXE Shell
item reboot               Reboot
item poweroff             Power Off
choose --timeout 10000 --default thinq-default selected || goto cancel
goto ${selected}

:thinq-default
set qemu_cmd "-m 2048 -smp 2"
goto boot

:thinq-4gb
set qemu_cmd "-m 4096 -smp 4"
goto boot

:thinq-8gb
set qemu_cmd "-m 8192 -smp 8"
goto boot

:thinq-custom
echo Enter custom QEMU command line options:
read qemu_cmd
goto boot

:boot
chain thinqalpine.ipxe

:shell
shell

:reboot
reboot

:poweroff
poweroff

:cancel
echo Boot cancelled
exit
EOF

    log_success "iPXE configuration files created in $config_dir"
}

# Validate iPXE builds
validate_builds() {
    log_info "Validating iPXE builds..."
    
    local files_to_check=(
        "$OUTPUT_DIR/undionly.kpxe"
    )
    
    case "$ARCH" in
        x86_64)
            files_to_check+=("$OUTPUT_DIR/ipxe-x86_64.efi")
            ;;
        aarch64)
            files_to_check+=("$OUTPUT_DIR/ipxe-aarch64.efi")
            ;;
    esac
    
    for file in "${files_to_check[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "Missing iPXE build: $file"
            return 1
        fi
        
        local size=$(stat -c%s "$file")
        log_info "$(basename "$file"): $size bytes"
    done
    
    log_success "iPXE build validation completed"
}

# Main function
main() {
    log_info "Building iPXE for $ARCH..."
    
    clone_ipxe
    prepare_embedded_script
    configure_ipxe_build
    build_ipxe_bios
    build_ipxe_uefi
    create_ipxe_configs
    validate_builds
    
    log_success "iPXE build completed for $ARCH"
}

# Run main function
main "$@"
