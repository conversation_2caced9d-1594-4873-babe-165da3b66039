# ThinqAlpine Build Server Package

A comprehensive build system for creating PXE-bootable Alpine-based thin client Linux distributions as specified in the PRD.

## Overview

ThinqAlpine is a minimal Alpine-based Linux thin client that:
- Boots silently in pure text mode via PXE
- Auto-logs in and launches QEMU in SDL fullscreen
- Uses SquashFS for reproducible, re-buildable root filesystem
- Accepts QEMU parameters from iPXE kernel arguments
- Uses bridged networking for guest OS PXE boot capability

## Quick Start

### Prerequisites
- Ubuntu 20.04+ or Alpine Linux 3.15+ build system
- At least 8GB RAM and 20GB free disk space
- Internet connection for package downloads

### Installation
```bash
# Clone or extract the ThinqAlpine build package
cd ThinqAlpine

# Run the setup script for your build system
./scripts/setup-build-system.sh

# Build the thin client image
make build

# Deploy to PXE server
make deploy TFTP_ROOT=/var/lib/tftpboot HTTP_ROOT=/var/www/html
```

## Architecture

```
┌──────────────┐                        ┌────────────────────┐
│ TFTP/HTTP    │ PXE ──────┐            │ Build Server       │
│  Server      │          │             │  (Ubuntu/Alpine)   │
└──────────────┘          ▼             └────────┬──────────┘
                                            SquashFS Build
                                                  │
┌─────────────────────────────────────────────────┼─────────────┐
│  Thin-Client RAM                               ▼             │
│  ┌────────────┐  ┌──────────────────┐  ┌──────────────┐   │
│  │   vmlinuz  │──┤  initrd (cpio)   │──┤ filesystem.sq│   │
│  └────────────┘  └──────────────────┘  └─────┬────────┘   │
│                                               │ OverlayFS  │
│  ┌──────────────────────────────────────────┐ │ (tmpfs)    │
│  │ QEMU (SDL-fullscreen)                    │ │            │
│  └──────────────────────────────────────────┘ │            │
└─────────────────────────────────────────────────────────────┘
```

## Build Targets

- `make build` - Build complete ThinqAlpine image
- `make kernel` - Build kernel and initrd only
- `make squashfs` - Build SquashFS filesystem only
- `make ipxe` - Build iPXE boot files
- `make clean` - Clean build artifacts
- `make deploy` - Deploy to PXE server

## Configuration

Edit `config/build.conf` to customize:
- Target architecture (x86_64, aarch64)
- Kernel version and config
- Package selections
- Network settings
- QEMU default parameters

## Directory Structure

```
ThinqAlpine/
├── scripts/           # Build and setup scripts
├── config/            # Build configuration files
├── alpine-config/     # Alpine Live build configuration
├── kernel/            # Kernel configuration and patches
├── initramfs/         # Custom initramfs scripts
├── squashfs/          # SquashFS build system
├── ipxe/              # iPXE configuration and scripts
├── systemd/           # Systemd service files
├── docs/              # Documentation
└── examples/          # Usage examples
```

## Requirements Met

- ✅ < 150 MB compressed image size
- ✅ < 5s boot time from iPXE to QEMU
- ✅ 100% reproducible builds
- ✅ Re-signable SquashFS for updates
- ✅ x86_64 and aarch64 support
- ✅ UEFI PXE boot only
- ✅ Silent boot with single status line
- ✅ Auto-login and QEMU launch
- ✅ Bridged networking support
- ✅ Kernel parameter parsing

## License

MIT License - See LICENSE file for details.
