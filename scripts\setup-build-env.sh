#!/bin/bash
# ThinqAlpine Build Environment Setup
# Sets up the Alpine build environment for the specified architecture

set -euo pipefail

# Script arguments
ARCH="${1:-x86_64}"
ALPINE_VERSION="${2:-3.18}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Validate architecture
validate_arch() {
    case "$ARCH" in
        x86_64|aarch64)
            log_info "Building for architecture: $ARCH"
            ;;
        *)
            log_error "Unsupported architecture: $ARCH"
            log_info "Supported architectures: x86_64, aarch64"
            exit 1
            ;;
    esac
}

# Setup Alpine chroot environment
setup_alpine_chroot() {
    local chroot_dir="$PROJECT_ROOT/build/alpine-$ARCH"
    
    log_info "Setting up Alpine $ALPINE_VERSION chroot for $ARCH..."
    
    # Remove existing chroot if present
    if [ -d "$chroot_dir" ]; then
        sudo rm -rf "$chroot_dir"
    fi
    
    mkdir -p "$chroot_dir"
    
    # Download Alpine mini root filesystem
    local alpine_url="$ALPINE_MIRROR/$ALPINE_BRANCH/releases/$ARCH/alpine-minirootfs-$ALPINE_VERSION.0-$ARCH.tar.gz"
    local alpine_tarball="$PROJECT_ROOT/cache/alpine-minirootfs-$ALPINE_VERSION.0-$ARCH.tar.gz"
    
    mkdir -p "$PROJECT_ROOT/cache"
    
    if [ ! -f "$alpine_tarball" ]; then
        log_info "Downloading Alpine mini root filesystem..."
        wget -O "$alpine_tarball" "$alpine_url"
    fi
    
    # Extract Alpine root filesystem
    log_info "Extracting Alpine root filesystem..."
    sudo tar -xzf "$alpine_tarball" -C "$chroot_dir"
    
    # Setup chroot environment
    log_info "Configuring chroot environment..."
    
    # Copy resolv.conf for network access
    sudo cp /etc/resolv.conf "$chroot_dir/etc/"
    
    # Setup repositories
    sudo tee "$chroot_dir/etc/apk/repositories" > /dev/null << EOF
$ALPINE_MIRROR/$ALPINE_BRANCH/main
$ALPINE_MIRROR/$ALPINE_BRANCH/community
EOF
    
    # Mount necessary filesystems
    sudo mount --bind /dev "$chroot_dir/dev"
    sudo mount --bind /proc "$chroot_dir/proc"
    sudo mount --bind /sys "$chroot_dir/sys"
    
    # Update package index
    sudo chroot "$chroot_dir" apk update
    
    # Install build dependencies in chroot
    sudo chroot "$chroot_dir" apk add --no-cache \
        alpine-sdk \
        build-base \
        git \
        wget \
        curl \
        rsync \
        squashfs-tools \
        linux-$KERNEL_FLAVOR \
        linux-firmware \
        mkinitfs \
        busybox \
        openrc \
        eudev
    
    log_success "Alpine chroot environment setup completed"
}

# Setup cross-compilation if needed
setup_cross_compilation() {
    if [ "$ARCH" != "$(uname -m)" ]; then
        log_info "Setting up cross-compilation for $ARCH..."
        
        case "$ARCH" in
            aarch64)
                sudo apt-get install -y gcc-aarch64-linux-gnu || \
                sudo apk add gcc-aarch64-none-elf || true
                export CROSS_COMPILE=aarch64-linux-gnu-
                ;;
            x86_64)
                # Usually native on most build systems
                ;;
        esac
        
        log_success "Cross-compilation setup completed"
    fi
}

# Setup QEMU user emulation for cross-arch builds
setup_qemu_user() {
    if [ "$ARCH" != "$(uname -m)" ]; then
        log_info "Setting up QEMU user emulation..."
        
        # Install qemu-user-static
        sudo apt-get install -y qemu-user-static binfmt-support || \
        sudo apk add qemu-$ARCH || true
        
        # Register binfmt handlers
        sudo update-binfmts --enable || true
        
        log_success "QEMU user emulation setup completed"
    fi
}

# Create build scripts in chroot
create_build_scripts() {
    local chroot_dir="$PROJECT_ROOT/build/alpine-$ARCH"
    
    log_info "Creating build scripts in chroot..."
    
    # Create build script directory
    sudo mkdir -p "$chroot_dir/build-scripts"
    
    # Copy build scripts
    sudo cp -r "$PROJECT_ROOT/scripts"/* "$chroot_dir/build-scripts/"
    sudo cp -r "$PROJECT_ROOT/config" "$chroot_dir/"
    
    # Make scripts executable
    sudo chmod +x "$chroot_dir/build-scripts"/*.sh
    
    log_success "Build scripts created in chroot"
}

# Cleanup function
cleanup() {
    local chroot_dir="$PROJECT_ROOT/build/alpine-$ARCH"
    
    if [ -d "$chroot_dir" ]; then
        log_info "Cleaning up mount points..."
        sudo umount "$chroot_dir/dev" 2>/dev/null || true
        sudo umount "$chroot_dir/proc" 2>/dev/null || true
        sudo umount "$chroot_dir/sys" 2>/dev/null || true
    fi
}

# Setup signal handlers
trap cleanup EXIT INT TERM

# Main function
main() {
    log_info "Setting up build environment for $ARCH (Alpine $ALPINE_VERSION)..."
    
    validate_arch
    setup_cross_compilation
    setup_qemu_user
    setup_alpine_chroot
    create_build_scripts
    
    log_success "Build environment setup completed for $ARCH"
    log_info "Chroot environment: $PROJECT_ROOT/build/alpine-$ARCH"
}

# Run main function
main "$@"
