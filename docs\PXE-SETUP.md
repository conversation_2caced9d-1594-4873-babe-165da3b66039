# ThinqAlpine PXE Server Setup Guide

This guide explains how to configure your PXE server infrastructure to boot ThinqAlpine thin clients.

## Overview

ThinqAlpine uses a standard PXE boot process:
1. Client requests DHCP lease
2. DHCP server provides next-server and boot filename
3. Client downloads iPXE bootloader via TFTP
4. iPXE downloads kernel, initrd, and SquashFS via HTTP
5. System boots and launches QEMU

## Required Services

### 1. DHCP Server
Provides IP addresses and PXE boot information

### 2. TFTP Server
Serves iPXE bootloader files

### 3. HTTP Server
Serves kernel, initrd, and SquashFS files

## DHCP Configuration

### ISC DHCP Server

#### Installation
```bash
# Ubuntu/Debian
sudo apt install isc-dhcp-server

# Alpine
sudo apk add dhcp
```

#### Configuration (`/etc/dhcp/dhcpd.conf`)
```bash
# Global options
option space ipxe;
option ipxe-encap-opts code 175 = encapsulate ipxe;
option arch code 93 = unsigned integer 16;

# Subnet configuration
subnet *********** netmask ************* {
    range ************* *************;
    option routers ***********;
    option domain-name-servers *******, *******;
    option broadcast-address *************;
    
    # PXE boot configuration
    next-server ***********0;  # Your TFTP/HTTP server IP
    
    # Architecture-specific boot files
    if exists user-class and option user-class = "iPXE" {
        # iPXE is already loaded, chain to boot script
        filename "http://***********0/thinq/boot.ipxe";
    } elsif option arch = 00:07 or option arch = 00:09 {
        # UEFI x86_64
        filename "ipxe-x86_64.efi";
    } elsif option arch = 00:0b {
        # UEFI aarch64  
        filename "ipxe-aarch64.efi";
    } else {
        # BIOS (legacy)
        filename "undionly.kpxe";
    }
    
    # ThinqAlpine-specific options
    option thinq-server "http://***********0";
    
    # Default QEMU parameters (can be overridden per client)
    option qemu_cmd "-m 2048 -smp 2";
}

# Per-client configuration examples
host thin-client-01 {
    hardware ethernet 00:11:22:33:44:55;
    fixed-address ***********01;
    option qemu_cmd "-m 4096 -smp 4 -boot order=nc";
}

host thin-client-02 {
    hardware ethernet 00:11:22:33:44:66;
    fixed-address ***********02;
    option qemu_cmd "-m 8192 -smp 8 -boot order=nc";
}
```

#### Start DHCP Service
```bash
# Ubuntu/Debian
sudo systemctl enable isc-dhcp-server
sudo systemctl start isc-dhcp-server

# Alpine
sudo rc-update add dhcpd
sudo rc-service dhcpd start
```

### Dnsmasq (Alternative)

#### Installation
```bash
# Ubuntu/Debian
sudo apt install dnsmasq

# Alpine
sudo apk add dnsmasq
```

#### Configuration (`/etc/dnsmasq.conf`)
```bash
# DHCP range
dhcp-range=*************,*************,12h

# Gateway and DNS
dhcp-option=3,***********
dhcp-option=6,*******,*******

# PXE boot configuration
dhcp-boot=tag:!ipxe,undionly.kpxe,***********0
dhcp-boot=tag:ipxe,http://***********0/thinq/boot.ipxe

# UEFI support
dhcp-match=set:efi-x86_64,option:client-arch,7
dhcp-match=set:efi-x86_64,option:client-arch,9
dhcp-boot=tag:efi-x86_64,ipxe-x86_64.efi,***********0

# Enable TFTP
enable-tftp
tftp-root=/var/lib/tftpboot
```

## TFTP Server Configuration

### tftpd-hpa (Recommended)

#### Installation
```bash
# Ubuntu/Debian
sudo apt install tftpd-hpa

# Alpine
sudo apk add tftp-hpa
```

#### Configuration (`/etc/default/tftpd-hpa`)
```bash
TFTP_USERNAME="tftp"
TFTP_DIRECTORY="/var/lib/tftpboot"
TFTP_ADDRESS="0.0.0.0:69"
TFTP_OPTIONS="--secure --create"
```

#### Start TFTP Service
```bash
# Ubuntu/Debian
sudo systemctl enable tftpd-hpa
sudo systemctl start tftpd-hpa

# Alpine
sudo rc-update add in.tftpd
sudo rc-service in.tftpd start
```

### File Permissions
```bash
sudo chown -R tftp:tftp /var/lib/tftpboot
sudo chmod -R 755 /var/lib/tftpboot
```

## HTTP Server Configuration

### Nginx (Recommended)

#### Installation
```bash
# Ubuntu/Debian
sudo apt install nginx

# Alpine
sudo apk add nginx
```

#### Configuration (`/etc/nginx/sites-available/thinqalpine`)
```nginx
server {
    listen 80;
    server_name _;
    root /var/www/html;
    
    # Enable directory listing for debugging
    autoindex on;
    autoindex_exact_size off;
    autoindex_localtime on;
    
    # ThinqAlpine files location
    location /thinq/ {
        alias /var/www/html/thinq/;
        
        # CORS headers for iPXE
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type";
        
        # Cache control
        location ~* \.(squashfs|img)$ {
            expires 1d;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(ipxe|json)$ {
            expires 1h;
            add_header Cache-Control "public";
        }
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "OK\n";
        add_header Content-Type text/plain;
    }
}
```

#### Enable Site
```bash
# Ubuntu/Debian
sudo ln -s /etc/nginx/sites-available/thinqalpine /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Alpine
sudo nginx -t
sudo rc-service nginx reload
```

### Apache (Alternative)

#### Installation
```bash
# Ubuntu/Debian
sudo apt install apache2

# Alpine
sudo apk add apache2
```

#### Configuration (`/etc/apache2/sites-available/thinqalpine.conf`)
```apache
<VirtualHost *:80>
    DocumentRoot /var/www/html
    
    # Enable directory browsing
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride None
        Require all granted
    </Directory>
    
    # ThinqAlpine specific settings
    <Directory /var/www/html/thinq>
        # CORS headers
        Header always set Access-Control-Allow-Origin "*"
        Header always set Access-Control-Allow-Methods "GET, OPTIONS"
        
        # Cache control
        <FilesMatch "\.(squashfs|img)$">
            ExpiresActive On
            ExpiresDefault "access plus 1 day"
        </FilesMatch>
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/thinqalpine_error.log
    CustomLog ${APACHE_LOG_DIR}/thinqalpine_access.log combined
</VirtualHost>
```

## Firewall Configuration

### UFW (Ubuntu)
```bash
sudo ufw allow 67/udp    # DHCP
sudo ufw allow 69/udp    # TFTP
sudo ufw allow 80/tcp    # HTTP
sudo ufw reload
```

### iptables (Alpine)
```bash
# Allow DHCP
iptables -A INPUT -p udp --dport 67 -j ACCEPT

# Allow TFTP
iptables -A INPUT -p udp --dport 69 -j ACCEPT

# Allow HTTP
iptables -A INPUT -p tcp --dport 80 -j ACCEPT

# Save rules
rc-service iptables save
```

## Testing PXE Setup

### 1. Test DHCP
```bash
# Check DHCP service
sudo systemctl status isc-dhcp-server  # or dhcpd

# Test DHCP lease
sudo dhclient -v eth0
```

### 2. Test TFTP
```bash
# Test TFTP access
echo "quit" | tftp localhost -c get undionly.kpxe /tmp/test.kpxe
ls -la /tmp/test.kpxe

# Test from remote client
echo "quit" | tftp ***********0 -c get undionly.kpxe /tmp/test.kpxe
```

### 3. Test HTTP
```bash
# Test HTTP access
curl -I http://localhost/thinq/version.json
wget -O /dev/null http://localhost/thinq/filesystem-x86_64.squashfs

# Test from remote client
curl -I http://***********0/thinq/version.json
```

### 4. Test PXE Boot
1. Configure a test VM or physical machine for PXE boot
2. Set network boot as first boot option
3. Boot the machine
4. Verify it receives DHCP lease and downloads iPXE
5. Check that ThinqAlpine boots successfully

## Advanced Configuration

### Load Balancing
Use multiple HTTP servers with a load balancer:

```nginx
upstream thinq_backend {
    server ***********0:80;
    server ***********1:80;
    server ************:80;
}

server {
    listen 80;
    location /thinq/ {
        proxy_pass http://thinq_backend;
    }
}
```

### HTTPS Support
```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /thinq/ {
        alias /var/www/html/thinq/;
    }
}
```

### Monitoring
```bash
# Monitor DHCP leases
tail -f /var/log/dhcp/dhcpd.log

# Monitor TFTP access
tail -f /var/log/syslog | grep tftpd

# Monitor HTTP access
tail -f /var/log/nginx/access.log | grep thinq
```

## Troubleshooting

### Common Issues

1. **No DHCP Response**
   - Check DHCP service status
   - Verify network interface configuration
   - Check firewall rules

2. **TFTP Timeout**
   - Verify TFTP service is running
   - Check file permissions
   - Test TFTP connectivity

3. **HTTP Download Fails**
   - Check HTTP server status
   - Verify file permissions
   - Test HTTP connectivity

4. **iPXE Script Errors**
   - Validate iPXE script syntax
   - Check file URLs are accessible
   - Review iPXE logs

See `docs/TROUBLESHOOTING.md` for detailed troubleshooting steps.
