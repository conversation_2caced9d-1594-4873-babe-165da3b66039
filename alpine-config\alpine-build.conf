# ThinqAlpine Alpine Build Configuration
# Configuration for building Alpine-based thin client

# === Build Settings ===
ALPINE_VERSION="3.18"
ALPINE_ARCH="x86_64"
ALPINE_FLAVOR="lts"
ALPINE_MIRROR="http://dl-cdn.alpinelinux.org/alpine"

# === Image Settings ===
IMAGE_NAME="thinqalpine"
IMAGE_VERSION="1.0"
BUILD_DATE=$(date -u +"%Y%m%d_%H%M%S")

# === Kernel Configuration ===
KERNEL_FLAVOR="lts"
KERNEL_MODULES="loop,squashfs,overlay,bridge,virtio,virtio_net,virtio_blk,virtio_scsi,kvm,kvm_intel,kvm_amd"
KERNEL_CMDLINE="quiet loglevel=0 rd.systemd.show_status=false console=tty1"

# === Init System Configuration ===
INIT_SYSTEM="openrc"
DEFAULT_RUNLEVEL="default"

# === User Configuration ===
ROOT_PASSWORD=""  # Empty = disabled
THIN_USER="thin"
THIN_UID="1001"
THIN_GID="1001"
THIN_HOME="/home/<USER>"
THIN_SHELL="/usr/bin/start-qemu.sh"

# === Network Configuration ===
HOSTNAME="thinqalpine"
DOMAIN=""
NETWORK_INTERFACE="eth0"
NETWORK_CONFIG="dhcp"
BRIDGE_NAME="br0"

# === Services Configuration ===
ENABLED_SERVICES="networking dhcpcd chronyd"
DISABLED_SERVICES="sshd crond"

# === Filesystem Configuration ===
ROOT_FS_TYPE="tmpfs"
ROOT_FS_SIZE="256M"
TMPFS_DIRS="/run /var/tmp /tmp /var/log"

# === SquashFS Configuration ===
SQUASHFS_COMPRESSION="xz"
SQUASHFS_BLOCK_SIZE="1048576"
SQUASHFS_COMP_OPTS="-Xdict-size 100%"

# === Security Configuration ===
DISABLE_ROOT_LOGIN="true"
READONLY_ROOT="true"
SECURE_BOOT="false"

# === QEMU Default Configuration ===
QEMU_BINARY="/usr/bin/qemu-system-x86_64"
QEMU_DEFAULT_ARGS="-display sdl -full-screen -enable-kvm"
QEMU_MEMORY="2048"
QEMU_CPUS="2"
QEMU_NETWORK="-netdev bridge,id=br0 -device virtio-net-pci,netdev=br0"

# === Build Optimization ===
PARALLEL_JOBS=$(nproc)
CCACHE_ENABLE="true"
STRIP_BINARIES="true"
COMPRESS_MODULES="true"

# === Debugging Options ===
DEBUG_BUILD="false"
INCLUDE_DEBUG_SYMBOLS="false"
ENABLE_SERIAL_CONSOLE="false"

# === Custom Hooks ===
PRE_BUILD_HOOK=""
POST_BUILD_HOOK=""
PRE_INSTALL_HOOK=""
POST_INSTALL_HOOK=""
