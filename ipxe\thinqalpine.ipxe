#!ipxe
# ThinqAlpine iPXE Boot Script
# Embedded script for PXE booting ThinqAlpine thin client

# Initialize networking
dhcp

# Display boot information
echo
echo ========================================
echo ThinqAlpine v1.0 PXE Boot
echo ========================================
echo MAC address: ${net0/mac}
echo IP address: ${ip}
echo Next server: ${next-server}
echo Architecture: ${buildarch}
echo ========================================

# Set architecture-specific variables
iseq ${buildarch} x86_64 && set arch x86_64 || set arch ${buildarch}
iseq ${buildarch} i386 && set arch x86_64 ||

# Server configuration
set server http://${next-server}
isset ${thinq-server} && set server ${thinq-server} ||

# File paths
set base-url ${server}/thinq
set vmlinuz ${base-url}/vmlinuz-${arch}
set initrd ${base-url}/initrd-${arch}
set squashfs ${base-url}/filesystem-${arch}.squashfs

# Kernel command line (PRD requirement F2 - suppress boot messages)
set cmdline quiet loglevel=0 rd.systemd.show_status=false console=tty1
set cmdline ${cmdline} modules=loop,squashfs,overlay,bridge,virtio

# Add SquashFS URL to kernel command line
set cmdline ${cmdline} squashfs_url=${squashfs}

# Parse custom QEMU command from DHCP/kernel parameters
# This allows field technicians to customize QEMU options (PRD User Story 2)
isset ${qemu_cmd} && set cmdline ${cmdline} qemu_cmd="${qemu_cmd}" ||

# Default QEMU command if none specified
isset ${qemu_cmd} || set qemu_cmd "-m 2048 -smp 2"
set cmdline ${cmdline} qemu_cmd="${qemu_cmd}"

# Memory and CPU defaults (can be overridden)
isset ${qemu_memory} && set qemu_mem ${qemu_memory} || set qemu_mem 2048
isset ${qemu_cpus} && set qemu_cpus ${qemu_cpus} || set qemu_cpus 2

# Network configuration for guest
isset ${qemu_net} && set qemu_network ${qemu_net} || set qemu_network "bridge"

# Add network configuration to kernel command line
set cmdline ${cmdline} qemu_memory=${qemu_mem} qemu_cpus=${qemu_cpus} qemu_network=${qemu_network}

# Build ID for tracking (PRD requirement F2)
isset ${build_id} && set cmdline ${cmdline} build_id=${build_id} ||

# Debug options (only if enabled)
isset ${thinq_debug} && set cmdline ${cmdline} thinq_debug=${thinq_debug} ||

# Display configuration before boot
echo
echo Boot Configuration:
echo ------------------
echo Kernel: ${vmlinuz}
echo Initrd: ${initrd}
echo SquashFS: ${squashfs}
echo Command line: ${cmdline}
echo QEMU command: ${qemu_cmd}
echo

# Optional: Pause for review (uncomment for debugging)
# echo Press any key to continue...
# prompt

# Download and verify files
echo Downloading kernel...
kernel ${vmlinuz} ${cmdline} || goto error

echo Downloading initrd...
initrd ${initrd} || goto error

echo Starting ThinqAlpine...
boot || goto error

# Error handling
:error
echo
echo ========================================
echo Boot Error
echo ========================================
echo Failed to boot ThinqAlpine
echo
echo Troubleshooting:
echo 1. Check network connectivity
echo 2. Verify server files exist:
echo    - ${vmlinuz}
echo    - ${initrd}
echo    - ${squashfs}
echo 3. Check DHCP configuration
echo 4. Verify iPXE script syntax
echo
echo Press any key to retry...
prompt
goto start

# Alternative boot methods
:fallback
echo Attempting fallback boot methods...

# Try different server if available
isset ${fallback-server} && set server ${fallback-server} && goto start ||

# Try TFTP fallback
echo Trying TFTP fallback...
set base-url tftp://${next-server}/thinq
goto start

:start
# Main boot sequence starts here
goto top

:top
# This is where the main script begins
# (iPXE scripts can have forward references)
