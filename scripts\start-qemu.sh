#!/bin/bash
# Thinq<PERSON><PERSON>pine QEMU Startup Script
# This script is the main entry point for the thin client
# It parses kernel parameters and launches QEMU with SDL fullscreen

set -euo pipefail

# Configuration
LOG_FILE="/run/ThinqAlpine.log"
BRIDGE_NAME="br0"
QEMU_BINARY="/usr/bin/qemu-system-x86_64"

# Logging functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$$] $1" | tee -a "$LOG_FILE"
}

log_error() {
    log "ERROR: $1"
}

log_info() {
    log "INFO: $1"
}

log_warning() {
    log "WARNING: $1"
}

# Display ThinqAlpine banner (PRD requirement F2)
display_banner() {
    local build_id="${BUILD_ID:-unknown}"
    echo "ThinqAlpine v1.0 $build_id"
    log_info "ThinqAlpine v1.0 $build_id started"
}

# Parse kernel command line for qemu_cmd parameter
parse_qemu_cmd() {
    local cmdline
    cmdline=$(cat /proc/cmdline)
    
    log_info "Kernel command line: $cmdline"
    
    # Extract qemu_cmd parameter
    if [[ $cmdline =~ qemu_cmd=\"([^\"]+)\" ]] || [[ $cmdline =~ qemu_cmd=([^[:space:]]+) ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        return 1
    fi
}

# Setup network bridge
setup_bridge() {
    log_info "Setting up network bridge: $BRIDGE_NAME"
    
    # Check if bridge already exists
    if ip link show "$BRIDGE_NAME" &>/dev/null; then
        log_info "Bridge $BRIDGE_NAME already exists"
        return 0
    fi
    
    # Find first physical network interface
    local physical_if
    physical_if=$(ip link show | grep -E '^[0-9]+: (eth|ens|enp)[0-9]' | head -1 | cut -d: -f2 | tr -d ' ')
    
    if [ -z "$physical_if" ]; then
        log_error "No physical network interface found"
        return 1
    fi
    
    log_info "Using physical interface: $physical_if"
    
    # Create bridge
    ip link add name "$BRIDGE_NAME" type bridge
    ip link set dev "$BRIDGE_NAME" up
    
    # Add physical interface to bridge
    ip link set dev "$physical_if" master "$BRIDGE_NAME"
    ip link set dev "$physical_if" up
    
    # Move IP configuration from physical interface to bridge
    local ip_addr
    ip_addr=$(ip addr show "$physical_if" | grep 'inet ' | awk '{print $2}' | head -1)
    
    if [ -n "$ip_addr" ]; then
        ip addr del "$ip_addr" dev "$physical_if" 2>/dev/null || true
        ip addr add "$ip_addr" dev "$BRIDGE_NAME"
    else
        # Use DHCP on bridge
        dhcpcd "$BRIDGE_NAME" &
    fi
    
    log_info "Bridge $BRIDGE_NAME setup completed"
}

# Build QEMU command line
build_qemu_command() {
    local qemu_cmd="$1"
    local full_cmd="$QEMU_BINARY"
    
    # Add default QEMU options (PRD requirement F5)
    full_cmd="$full_cmd -display sdl -full-screen -enable-kvm"
    full_cmd="$full_cmd -netdev bridge,id=br0,br=$BRIDGE_NAME -device virtio-net-pci,netdev=br0"
    
    # Parse and add custom QEMU options
    if [ -n "$qemu_cmd" ]; then
        # Remove any existing conflicting options from custom command
        qemu_cmd=$(echo "$qemu_cmd" | sed -E 's/-display [^[:space:]]+//g')
        qemu_cmd=$(echo "$qemu_cmd" | sed -E 's/-full-screen//g')
        qemu_cmd=$(echo "$qemu_cmd" | sed -E 's/-enable-kvm//g')
        
        # Add custom options
        full_cmd="$full_cmd $qemu_cmd"
    fi
    
    echo "$full_cmd"
}

# Launch QEMU with restart loop (PRD requirement - handle crashes)
launch_qemu() {
    local qemu_cmd="$1"
    local full_cmd
    full_cmd=$(build_qemu_command "$qemu_cmd")
    
    log_info "QEMU command: $full_cmd"
    
    # Infinite restart loop
    while true; do
        log_info "Starting QEMU..."
        
        # Launch QEMU
        if eval "$full_cmd"; then
            log_info "QEMU exited normally"
        else
            local exit_code=$?
            log_error "QEMU exited with code $exit_code"
        fi
        
        log_info "Restarting QEMU in 1 second..."
        sleep 1
    done
}

# Emergency shell (PRD requirement - fail fast if no qemu_cmd)
emergency_shell() {
    log_error "No qemu_cmd parameter found in kernel command line"
    log_error "Dropping to emergency shell"
    
    echo ""
    echo "=== ThinqAlpine Emergency Shell ==="
    echo "No qemu_cmd parameter was provided in the kernel command line."
    echo "Please check your iPXE configuration."
    echo ""
    echo "Example iPXE configuration:"
    echo "kernel vmlinuz quiet loglevel=0 qemu_cmd=\"-m 2048 -smp 2\""
    echo "initrd initrd"
    echo "boot"
    echo ""
    echo "Type 'exit' to reboot or 'poweroff' to shutdown."
    echo ""
    
    # Start emergency shell
    exec /bin/sh
}

# Handle Ctrl+Alt+Del (PRD requirement F7)
setup_signal_handlers() {
    trap 'log_info "Received shutdown signal"; poweroff' TERM INT
}

# Validate QEMU binary and dependencies
validate_environment() {
    log_info "Validating environment..."
    
    # Check QEMU binary
    if [ ! -x "$QEMU_BINARY" ]; then
        log_error "QEMU binary not found: $QEMU_BINARY"
        return 1
    fi
    
    # Check KVM support
    if [ ! -e /dev/kvm ]; then
        log_warning "KVM device not found, hardware acceleration disabled"
    fi
    
    # Check SDL support
    if [ -z "${DISPLAY:-}" ] && [ -z "${WAYLAND_DISPLAY:-}" ]; then
        log_warning "No display environment detected"
    fi
    
    log_info "Environment validation completed"
}

# Main function
main() {
    # Setup logging
    mkdir -p "$(dirname "$LOG_FILE")"
    exec 2> >(tee -a "$LOG_FILE")
    
    # Display banner
    display_banner
    
    # Setup signal handlers
    setup_signal_handlers
    
    # Validate environment
    if ! validate_environment; then
        emergency_shell
    fi
    
    # Setup network bridge (PRD requirement F6)
    if ! setup_bridge; then
        log_error "Failed to setup network bridge"
        emergency_shell
    fi
    
    # Parse QEMU command from kernel parameters (PRD requirement F4)
    local qemu_cmd
    if ! qemu_cmd=$(parse_qemu_cmd); then
        emergency_shell
    fi
    
    log_info "Parsed qemu_cmd: $qemu_cmd"
    
    # Launch QEMU (PRD requirement F5)
    launch_qemu "$qemu_cmd"
}

# Run main function if script is executed directly
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
