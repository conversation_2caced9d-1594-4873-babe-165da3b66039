# ThinqAlpine Build System Makefile
# Builds PXE-bootable Alpine-based thin client

# Configuration
include config/build.conf

# Build directories
BUILD_DIR := build
ALPINE_DIR := $(BUILD_DIR)/alpine
KERNEL_DIR := $(BUILD_DIR)/kernel
INITRAMFS_DIR := $(BUILD_DIR)/initramfs
SQUASHFS_DIR := $(BUILD_DIR)/squashfs
IPXE_DIR := $(BUILD_DIR)/ipxe
OUTPUT_DIR := $(BUILD_DIR)/output

# Target architecture
ARCH ?= x86_64
ALPINE_VERSION ?= 3.18
KERNEL_VERSION ?= 6.1

# Output files
VMLINUZ := $(OUTPUT_DIR)/vmlinuz-$(ARCH)
INITRD := $(OUTPUT_DIR)/initrd-$(ARCH)
SQUASHFS := $(OUTPUT_DIR)/filesystem-$(ARCH).squashfs
IPXE_EFI := $(OUTPUT_DIR)/ipxe-$(ARCH).efi
IPXE_BIOS := $(OUTPUT_DIR)/undionly.kpxe

.PHONY: all build clean deploy setup kernel initramfs squashfs ipxe

# Default target
all: build

# Main build target
build: setup kernel initramfs squashfs ipxe
	@echo "=== ThinqAlpine Build Complete ==="
	@echo "Architecture: $(ARCH)"
	@echo "Kernel: $(KERNEL_VERSION)"
	@echo "Alpine: $(ALPINE_VERSION)"
	@echo ""
	@echo "Output files:"
	@ls -lh $(OUTPUT_DIR)/
	@echo ""
	@echo "Total compressed size:"
	@du -sh $(OUTPUT_DIR)/ | cut -f1

# Setup build environment
setup:
	@echo "=== Setting up build environment ==="
	./scripts/setup-build-env.sh $(ARCH) $(ALPINE_VERSION)
	mkdir -p $(BUILD_DIR) $(ALPINE_DIR) $(KERNEL_DIR) $(INITRAMFS_DIR) $(SQUASHFS_DIR) $(IPXE_DIR) $(OUTPUT_DIR)

# Build kernel and modules
kernel: setup
	@echo "=== Building kernel $(KERNEL_VERSION) for $(ARCH) ==="
	./scripts/build-kernel.sh $(ARCH) $(KERNEL_VERSION) $(KERNEL_DIR) $(OUTPUT_DIR)

# Build initramfs
initramfs: setup
	@echo "=== Building initramfs for $(ARCH) ==="
	./scripts/build-initramfs.sh $(ARCH) $(INITRAMFS_DIR) $(OUTPUT_DIR)

# Build SquashFS filesystem
squashfs: setup
	@echo "=== Building SquashFS filesystem for $(ARCH) ==="
	./scripts/build-squashfs.sh $(ARCH) $(ALPINE_VERSION) $(SQUASHFS_DIR) $(OUTPUT_DIR)

# Build iPXE boot files
ipxe: setup
	@echo "=== Building iPXE boot files for $(ARCH) ==="
	./scripts/build-ipxe.sh $(ARCH) $(IPXE_DIR) $(OUTPUT_DIR)

# Clean build artifacts
clean:
	@echo "=== Cleaning build artifacts ==="
	rm -rf $(BUILD_DIR)
	docker system prune -f 2>/dev/null || true

# Deploy to PXE server
deploy: build
	@echo "=== Deploying to PXE server ==="
	@if [ -z "$(TFTP_ROOT)" ] || [ -z "$(HTTP_ROOT)" ]; then \
		echo "Error: TFTP_ROOT and HTTP_ROOT must be specified"; \
		echo "Usage: make deploy TFTP_ROOT=/var/lib/tftpboot HTTP_ROOT=/var/www/html"; \
		exit 1; \
	fi
	./scripts/deploy.sh $(OUTPUT_DIR) $(TFTP_ROOT) $(HTTP_ROOT) $(ARCH)

# Development targets
dev-shell:
	@echo "=== Starting development shell ==="
	./scripts/dev-shell.sh $(ARCH) $(ALPINE_VERSION)

test:
	@echo "=== Running tests ==="
	./scripts/test.sh $(OUTPUT_DIR) $(ARCH)

# Architecture-specific targets
x86_64:
	$(MAKE) build ARCH=x86_64

aarch64:
	$(MAKE) build ARCH=aarch64

# Version info
version:
	@echo "ThinqAlpine Build System v1.0"
	@echo "Target: $(ARCH)"
	@echo "Alpine: $(ALPINE_VERSION)"
	@echo "Kernel: $(KERNEL_VERSION)"

# Help target
help:
	@echo "ThinqAlpine Build System"
	@echo ""
	@echo "Targets:"
	@echo "  build     - Build complete ThinqAlpine image (default)"
	@echo "  kernel    - Build kernel and initramfs only"
	@echo "  squashfs  - Build SquashFS filesystem only"
	@echo "  ipxe      - Build iPXE boot files only"
	@echo "  clean     - Clean all build artifacts"
	@echo "  deploy    - Deploy to PXE server"
	@echo "  test      - Run build tests"
	@echo "  x86_64    - Build for x86_64 architecture"
	@echo "  aarch64   - Build for aarch64 architecture"
	@echo ""
	@echo "Variables:"
	@echo "  ARCH=x86_64|aarch64    - Target architecture"
	@echo "  ALPINE_VERSION=3.18    - Alpine Linux version"
	@echo "  KERNEL_VERSION=6.1     - Kernel version"
	@echo "  TFTP_ROOT=/path        - TFTP server root (for deploy)"
	@echo "  HTTP_ROOT=/path        - HTTP server root (for deploy)"
