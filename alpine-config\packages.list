# ThinqAlpine Package List
# Minimal package set for PXE-bootable thin client

# === Core System Packages ===
alpine-base
busybox
musl
alpine-keys
apk-tools
openrc
eudev
util-linux

# === Boot and Kernel Packages ===
linux-lts
linux-firmware
mkinitfs
syslinux

# === QEMU and Virtualization ===
qemu-system-x86_64
qemu-modules
qemu-hw-display-virtio-gpu
qemu-hw-display-virtio-vga

# === Graphics and Display (Minimal SDL) ===
mesa-dri-gallium
libsdl2
libx11
libxext
libxrandr
libxinerama
libxcursor
libxi
libxss

# === Network Packages ===
dhcpcd
bridge-utils
iptables
iproute2

# === System Utilities ===
coreutil-fmt
findutils
grep
sed
gawk
tar
gzip
xz

# === Logging ===
rsyslog

# === Security ===
sudo

# === Development Tools (Optional - can be removed for production) ===
# Uncomment these for debugging builds
# nano
# vim
# strace
# tcpdump
# htop
