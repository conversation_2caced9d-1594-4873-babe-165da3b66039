# ThinqAlpine Build Configuration
# This file contains all build-time configuration options

# === Architecture Settings ===
ARCH ?= x86_64
SUPPORTED_ARCHS := x86_64 aarch64

# === Alpine Linux Settings ===
ALPINE_VERSION ?= 3.18
ALPINE_MIRROR := http://dl-cdn.alpinelinux.org/alpine
ALPINE_BRANCH := v$(ALPINE_VERSION)

# === Kernel Settings ===
KERNEL_VERSION ?= 6.1
KERNEL_FLAVOR := lts
KERNEL_CMDLINE := quiet loglevel=0 rd.systemd.show_status=false console=tty1

# === Image Size Limits (PRD Requirements) ===
MAX_COMPRESSED_SIZE_MB := 150
MAX_RAM_FOOTPRINT_MB := 256
TARGET_BOOT_TIME_SEC := 5

# === Build Environment ===
BUILD_USER := thinq-builder
BUILD_UID := 1000
BUILD_GID := 1000

# === Package Lists ===
# Core system packages (minimal Alpine base)
CORE_PACKAGES := \
	alpine-base \
	busybox \
	musl \
	alpine-keys \
	apk-tools

# Boot and init packages
BOOT_PACKAGES := \
	linux-$(KERNEL_FLAVOR) \
	linux-firmware \
	mkinitfs \
	openrc \
	eudev

# QEMU and virtualization packages
QEMU_PACKAGES := \
	qemu-system-x86_64 \
	qemu-modules \
	bridge-utils \
	iptables

# Graphics and SDL packages (minimal for QEMU SDL)
GRAPHICS_PACKAGES := \
	mesa-dri-gallium \
	libsdl2 \
	libx11 \
	libxext

# Network packages
NETWORK_PACKAGES := \
	dhcpcd \
	openssh-client \
	wget \
	curl

# === User Configuration ===
THIN_USER := thin
THIN_UID := 1001
THIN_GID := 1001
THIN_SHELL := /usr/bin/start-qemu.sh

# === QEMU Default Configuration ===
QEMU_DEFAULT_MEMORY := 2048
QEMU_DEFAULT_CPUS := 2
QEMU_DEFAULT_DISPLAY := sdl
QEMU_ENABLE_KVM := true
QEMU_NETWORK_BRIDGE := br0

# === Network Bridge Configuration ===
BRIDGE_NAME := br0
BRIDGE_IP := dhcp
AUTO_CREATE_BRIDGE := true

# === SquashFS Configuration ===
SQUASHFS_COMPRESSION := xz
SQUASHFS_BLOCK_SIZE := 1048576
SQUASHFS_COMP_OPTS := -Xdict-size 100%

# === iPXE Configuration ===
IPXE_TIMEOUT := 5
IPXE_DEFAULT_SERVER := ${next-server}
IPXE_SCRIPT_URL := 

# === Security Settings ===
DISABLE_ROOT_LOGIN := true
DISABLE_PASSWORD_AUTH := true
READONLY_ROOT := true
TMPFS_DIRS := /run /var/tmp /tmp

# === Logging Configuration ===
LOG_LEVEL := warn
LOG_FILE := /run/ThinqAlpine.log
SYSTEMD_LOG_TARGET := null

# === Build Reproducibility ===
SOURCE_DATE_EPOCH := $(shell date +%s)
BUILD_ID := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIMESTAMP := $(shell date -u +"%Y%m%d_%H%M%S")

# === Development Settings ===
DEBUG_BUILD := false
ENABLE_SSH_DEBUG := false
INCLUDE_DEBUG_TOOLS := false

# === Deployment Settings ===
DEFAULT_TFTP_ROOT := /var/lib/tftpboot
DEFAULT_HTTP_ROOT := /var/www/html
PXE_SUBDIR := thinq

# === Docker Build Settings ===
DOCKER_REGISTRY := 
DOCKER_TAG := thinqalpine-builder:$(ALPINE_VERSION)
USE_DOCKER := auto

# === Validation Settings ===
VALIDATE_SIZE_LIMITS := true
VALIDATE_BOOT_TIME := false
RUN_INTEGRATION_TESTS := false

# === Custom Hooks ===
PRE_BUILD_HOOK := 
POST_BUILD_HOOK := 
PRE_DEPLOY_HOOK := 
POST_DEPLOY_HOOK := 

# === Advanced Settings ===
PARALLEL_JOBS := $(shell nproc)
CCACHE_ENABLE := true
CCACHE_DIR := $(HOME)/.ccache/thinqalpine

# === Feature Flags ===
ENABLE_SERIAL_CONSOLE := false
ENABLE_VNC_FALLBACK := false
ENABLE_HTTP_BOOT := false
ENABLE_SECURE_BOOT := false

# === Version Information ===
THINQALPINE_VERSION := 1.0
THINQALPINE_CODENAME := ThinqAlpine
BUILD_DATE := $(shell date -u +"%Y-%m-%d %H:%M:%S UTC")

# === Export all variables ===
export
