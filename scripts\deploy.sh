#!/bin/bash
# ThinqAlpine Deployment Script
# Deploys built images to PXE server (TFTP and HTTP)

set -euo pipefail

# Script arguments
OUTPUT_DIR="${1:-build/output}"
TFTP_ROOT="${2:-/var/lib/tftpboot}"
HTTP_ROOT="${3:-/var/www/html}"
ARCH="${4:-x86_64}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if running as root or with sudo
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root or with sudo"
        log_info "Usage: sudo $0 $OUTPUT_DIR $TFTP_ROOT $HTTP_ROOT $ARCH"
        exit 1
    fi
}

# Validate deployment directories
validate_directories() {
    log_info "Validating deployment directories..."
    
    # Check output directory
    if [ ! -d "$OUTPUT_DIR" ]; then
        log_error "Output directory not found: $OUTPUT_DIR"
        log_info "Run 'make build' first to create the images"
        exit 1
    fi
    
    # Check TFTP root
    if [ ! -d "$TFTP_ROOT" ]; then
        log_warning "TFTP root directory not found: $TFTP_ROOT"
        log_info "Creating TFTP root directory..."
        mkdir -p "$TFTP_ROOT"
    fi
    
    # Check HTTP root
    if [ ! -d "$HTTP_ROOT" ]; then
        log_warning "HTTP root directory not found: $HTTP_ROOT"
        log_info "Creating HTTP root directory..."
        mkdir -p "$HTTP_ROOT"
    fi
    
    log_success "Directory validation completed"
}

# Validate built files
validate_built_files() {
    log_info "Validating built files..."
    
    local required_files=(
        "$OUTPUT_DIR/vmlinuz-$ARCH"
        "$OUTPUT_DIR/initrd-$ARCH"
        "$OUTPUT_DIR/filesystem-$ARCH.squashfs"
        "$OUTPUT_DIR/undionly.kpxe"
    )
    
    # Add architecture-specific iPXE files
    case "$ARCH" in
        x86_64)
            required_files+=("$OUTPUT_DIR/ipxe-x86_64.efi")
            ;;
        aarch64)
            required_files+=("$OUTPUT_DIR/ipxe-aarch64.efi")
            ;;
    esac
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "Required file not found: $file"
            log_info "Run 'make build' to create all required files"
            exit 1
        fi
        
        local size=$(stat -c%s "$file")
        log_info "$(basename "$file"): $size bytes"
    done
    
    log_success "File validation completed"
}

# Deploy TFTP files
deploy_tftp_files() {
    log_info "Deploying TFTP files to $TFTP_ROOT..."
    
    # Create ThinqAlpine directory in TFTP root
    local tftp_thinq_dir="$TFTP_ROOT/thinq"
    mkdir -p "$tftp_thinq_dir"
    
    # Copy iPXE boot files
    cp "$OUTPUT_DIR/undionly.kpxe" "$TFTP_ROOT/"
    
    case "$ARCH" in
        x86_64)
            cp "$OUTPUT_DIR/ipxe-x86_64.efi" "$TFTP_ROOT/"
            ;;
        aarch64)
            cp "$OUTPUT_DIR/ipxe-aarch64.efi" "$TFTP_ROOT/"
            ;;
    esac
    
    # Copy configuration files if they exist
    if [ -d "$OUTPUT_DIR/ipxe-configs" ]; then
        cp -r "$OUTPUT_DIR/ipxe-configs"/* "$tftp_thinq_dir/"
    fi
    
    # Set proper permissions
    chmod 644 "$TFTP_ROOT"/*.{kpxe,efi} 2>/dev/null || true
    chown -R nobody:nogroup "$TFTP_ROOT" 2>/dev/null || true
    
    log_success "TFTP files deployed"
}

# Deploy HTTP files
deploy_http_files() {
    log_info "Deploying HTTP files to $HTTP_ROOT..."
    
    # Create ThinqAlpine directory in HTTP root
    local http_thinq_dir="$HTTP_ROOT/thinq"
    mkdir -p "$http_thinq_dir"
    
    # Copy kernel and initrd
    cp "$OUTPUT_DIR/vmlinuz-$ARCH" "$http_thinq_dir/"
    cp "$OUTPUT_DIR/initrd-$ARCH" "$http_thinq_dir/"
    
    # Copy SquashFS filesystem
    cp "$OUTPUT_DIR/filesystem-$ARCH.squashfs" "$http_thinq_dir/"
    
    # Create version info file
    create_version_info "$http_thinq_dir"
    
    # Create boot script
    create_boot_script "$http_thinq_dir"
    
    # Set proper permissions
    chmod 644 "$http_thinq_dir"/*
    chown -R www-data:www-data "$http_thinq_dir" 2>/dev/null || \
    chown -R apache:apache "$http_thinq_dir" 2>/dev/null || \
    chown -R nginx:nginx "$http_thinq_dir" 2>/dev/null || true
    
    log_success "HTTP files deployed"
}

# Create version info file
create_version_info() {
    local target_dir="$1"
    
    log_info "Creating version info file..."
    
    cat > "$target_dir/version.json" << EOF
{
    "name": "ThinqAlpine",
    "version": "$THINQALPINE_VERSION",
    "build_id": "$BUILD_ID",
    "build_date": "$BUILD_DATE",
    "architecture": "$ARCH",
    "alpine_version": "$ALPINE_VERSION",
    "kernel_version": "$KERNEL_VERSION",
    "files": {
        "kernel": "vmlinuz-$ARCH",
        "initrd": "initrd-$ARCH",
        "squashfs": "filesystem-$ARCH.squashfs"
    },
    "checksums": {
        "kernel": "$(sha256sum "$target_dir/vmlinuz-$ARCH" | cut -d' ' -f1)",
        "initrd": "$(sha256sum "$target_dir/initrd-$ARCH" | cut -d' ' -f1)",
        "squashfs": "$(sha256sum "$target_dir/filesystem-$ARCH.squashfs" | cut -d' ' -f1)"
    }
}
EOF
    
    log_success "Version info created"
}

# Create boot script
create_boot_script() {
    local target_dir="$1"
    
    log_info "Creating boot script..."
    
    cat > "$target_dir/boot.ipxe" << EOF
#!ipxe
# ThinqAlpine Dynamic Boot Script
# Generated on $(date)

# Set base URL
set base-url \${server}/thinq

# Architecture detection
iseq \${buildarch} x86_64 && set arch x86_64 || set arch \${buildarch}

# File URLs
set vmlinuz \${base-url}/vmlinuz-\${arch}
set initrd \${base-url}/initrd-\${arch}
set squashfs \${base-url}/filesystem-\${arch}.squashfs

# Default QEMU command (can be overridden)
isset \${qemu_cmd} || set qemu_cmd "-m 2048 -smp 2"

# Kernel command line
set cmdline quiet loglevel=0 rd.systemd.show_status=false console=tty1
set cmdline \${cmdline} squashfs_url=\${squashfs} qemu_cmd="\${qemu_cmd}"

# Boot
echo Booting ThinqAlpine v$THINQALPINE_VERSION (\${arch})...
kernel \${vmlinuz} \${cmdline}
initrd \${initrd}
boot
EOF
    
    log_success "Boot script created"
}

# Create deployment summary
create_deployment_summary() {
    log_info "Creating deployment summary..."
    
    local summary_file="$PROJECT_ROOT/deployment-summary.txt"
    
    cat > "$summary_file" << EOF
ThinqAlpine Deployment Summary
==============================

Deployment Date: $(date)
Architecture: $ARCH
Version: $THINQALPINE_VERSION
Build ID: $BUILD_ID

TFTP Deployment:
- Root: $TFTP_ROOT
- Files:
  * undionly.kpxe (BIOS boot)
  * ipxe-$ARCH.efi (UEFI boot)

HTTP Deployment:
- Root: $HTTP_ROOT/thinq
- Files:
  * vmlinuz-$ARCH ($(stat -c%s "$OUTPUT_DIR/vmlinuz-$ARCH" | numfmt --to=iec))
  * initrd-$ARCH ($(stat -c%s "$OUTPUT_DIR/initrd-$ARCH" | numfmt --to=iec))
  * filesystem-$ARCH.squashfs ($(stat -c%s "$OUTPUT_DIR/filesystem-$ARCH.squashfs" | numfmt --to=iec))
  * version.json
  * boot.ipxe

Total Size: $(du -sh "$HTTP_ROOT/thinq" | cut -f1)

DHCP Configuration:
Add the following to your DHCP server configuration:

next-server <your-server-ip>;

if exists user-class and option user-class = "iPXE" {
    filename "http://<your-server-ip>/thinq/boot.ipxe";
} elsif option arch = 00:07 or option arch = 00:09 {
    filename "ipxe-x86_64.efi";
} else {
    filename "undionly.kpxe";
}

Testing:
1. Configure a client to PXE boot
2. Ensure DHCP provides the correct next-server and filename
3. Client should boot to ThinqAlpine and launch QEMU

Troubleshooting:
- Check TFTP server is running and accessible
- Check HTTP server is running and serving files
- Verify firewall allows TFTP (69) and HTTP (80) traffic
- Check file permissions and ownership
EOF
    
    echo "Deployment summary saved to: $summary_file"
    cat "$summary_file"
}

# Test deployment
test_deployment() {
    log_info "Testing deployment..."
    
    # Test TFTP access
    if command -v tftp >/dev/null; then
        echo "quit" | tftp localhost -c get undionly.kpxe /tmp/test.kpxe 2>/dev/null && {
            log_success "TFTP test passed"
            rm -f /tmp/test.kpxe
        } || log_warning "TFTP test failed"
    fi
    
    # Test HTTP access
    if command -v curl >/dev/null; then
        curl -s -I "http://localhost/thinq/version.json" >/dev/null && {
            log_success "HTTP test passed"
        } || log_warning "HTTP test failed"
    fi
    
    log_info "Deployment testing completed"
}

# Main function
main() {
    log_info "Deploying ThinqAlpine for $ARCH..."
    log_info "Output: $OUTPUT_DIR"
    log_info "TFTP Root: $TFTP_ROOT"
    log_info "HTTP Root: $HTTP_ROOT"
    
    check_permissions
    validate_directories
    validate_built_files
    deploy_tftp_files
    deploy_http_files
    create_deployment_summary
    test_deployment
    
    log_success "ThinqAlpine deployment completed successfully!"
    log_info "Your thin clients can now PXE boot ThinqAlpine"
}

# Run main function
main "$@"
