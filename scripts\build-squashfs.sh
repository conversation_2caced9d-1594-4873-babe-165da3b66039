#!/bin/bash
# ThinqAlpine SquashFS Build Script
# Creates the compressed root filesystem

set -euo pipefail

# Script arguments
ARCH="${1:-x86_64}"
ALPINE_VERSION="${2:-3.18}"
SQUASHFS_DIR="${3:-build/squashfs}"
OUTPUT_DIR="${4:-build/output}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"
source "$PROJECT_ROOT/alpine-config/alpine-build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Create Alpine root filesystem
create_alpine_rootfs() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Creating Alpine root filesystem..."
    
    # Clean and create rootfs directory
    rm -rf "$rootfs_dir"
    mkdir -p "$rootfs_dir"
    
    # Download and extract Alpine mini root filesystem
    local alpine_url="$ALPINE_MIRROR/v$ALPINE_VERSION/releases/$ARCH/alpine-minirootfs-$ALPINE_VERSION.0-$ARCH.tar.gz"
    local alpine_tarball="$PROJECT_ROOT/cache/alpine-minirootfs-$ALPINE_VERSION.0-$ARCH.tar.gz"
    
    mkdir -p "$PROJECT_ROOT/cache"
    
    if [ ! -f "$alpine_tarball" ]; then
        log_info "Downloading Alpine mini root filesystem..."
        wget -O "$alpine_tarball" "$alpine_url"
    fi
    
    # Extract Alpine root filesystem
    tar -xzf "$alpine_tarball" -C "$rootfs_dir"
    
    log_success "Alpine root filesystem created"
}

# Configure Alpine repositories
configure_repositories() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Configuring Alpine repositories..."
    
    cat > "$rootfs_dir/etc/apk/repositories" << EOF
$ALPINE_MIRROR/v$ALPINE_VERSION/main
$ALPINE_MIRROR/v$ALPINE_VERSION/community
EOF
    
    log_success "Repositories configured"
}

# Install packages in chroot
install_packages() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Installing packages in chroot..."
    
    # Setup chroot environment
    cp /etc/resolv.conf "$rootfs_dir/etc/"
    mount --bind /dev "$rootfs_dir/dev"
    mount --bind /proc "$rootfs_dir/proc"
    mount --bind /sys "$rootfs_dir/sys"
    
    # Update package index
    chroot "$rootfs_dir" apk update
    
    # Install packages from list
    local packages=$(grep -v '^#' "$PROJECT_ROOT/alpine-config/packages.list" | grep -v '^$' | tr '\n' ' ')
    chroot "$rootfs_dir" apk add --no-cache $packages
    
    # Cleanup
    umount "$rootfs_dir/dev" "$rootfs_dir/proc" "$rootfs_dir/sys"
    rm -f "$rootfs_dir/etc/resolv.conf"
    
    log_success "Packages installed"
}

# Configure system
configure_system() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Configuring system..."
    
    # Set hostname
    echo "$HOSTNAME" > "$rootfs_dir/etc/hostname"
    
    # Configure hosts file
    cat > "$rootfs_dir/etc/hosts" << EOF
127.0.0.1    localhost localhost.localdomain
*********    $HOSTNAME $HOSTNAME.localdomain
::1          localhost ipv6-localhost ipv6-loopback
ff02::1      ipv6-allnodes
ff02::2      ipv6-allrouters
EOF
    
    # Configure network interface
    mkdir -p "$rootfs_dir/etc/network"
    cat > "$rootfs_dir/etc/network/interfaces" << EOF
auto lo
iface lo inet loopback

auto $NETWORK_INTERFACE
iface $NETWORK_INTERFACE inet dhcp
EOF
    
    # Configure inittab for auto-login
    sed -i 's/tty1::respawn:\/sbin\/getty 38400 tty1/tty1::respawn:\/bin\/login -f thin/' "$rootfs_dir/etc/inittab"
    
    # Disable unused ttys
    sed -i '/tty[2-6]/d' "$rootfs_dir/etc/inittab"
    
    log_success "System configured"
}

# Create thin user
create_thin_user() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Creating thin user..."
    
    # Add thin user
    chroot "$rootfs_dir" adduser -D -u "$THIN_UID" -s "$THIN_SHELL" "$THIN_USER"
    
    # Add to necessary groups
    chroot "$rootfs_dir" addgroup "$THIN_USER" video
    chroot "$rootfs_dir" addgroup "$THIN_USER" audio
    chroot "$rootfs_dir" addgroup "$THIN_USER" input
    
    # Create home directory structure
    mkdir -p "$rootfs_dir$THIN_HOME"
    chown "$THIN_UID:$THIN_GID" "$rootfs_dir$THIN_HOME"
    
    log_success "Thin user created"
}

# Install custom scripts and services
install_custom_files() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Installing custom files..."
    
    # Copy systemd services
    cp -r "$PROJECT_ROOT/systemd"/* "$rootfs_dir/etc/init.d/" 2>/dev/null || true
    
    # Copy custom scripts
    mkdir -p "$rootfs_dir/usr/bin"
    cp "$PROJECT_ROOT/scripts/start-qemu.sh" "$rootfs_dir/usr/bin/"
    cp "$PROJECT_ROOT/scripts/setup-bridge.sh" "$rootfs_dir/usr/bin/"
    chmod +x "$rootfs_dir/usr/bin/start-qemu.sh"
    chmod +x "$rootfs_dir/usr/bin/setup-bridge.sh"
    
    # Copy configuration files
    mkdir -p "$rootfs_dir/etc/thinqalpine"
    cp "$PROJECT_ROOT/config/build.conf" "$rootfs_dir/etc/thinqalpine/"
    
    log_success "Custom files installed"
}

# Configure services
configure_services() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Configuring services..."
    
    # Enable required services
    for service in $ENABLED_SERVICES; do
        chroot "$rootfs_dir" rc-update add "$service" default 2>/dev/null || true
    done
    
    # Disable unnecessary services
    for service in $DISABLED_SERVICES; do
        chroot "$rootfs_dir" rc-update del "$service" default 2>/dev/null || true
    done
    
    log_success "Services configured"
}

# Optimize filesystem
optimize_filesystem() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    
    log_info "Optimizing filesystem..."
    
    # Remove unnecessary files
    rm -rf "$rootfs_dir/var/cache/apk"/*
    rm -rf "$rootfs_dir/tmp"/*
    rm -rf "$rootfs_dir/var/tmp"/*
    
    # Strip binaries if enabled
    if [ "$STRIP_BINARIES" = "true" ]; then
        find "$rootfs_dir" -type f -executable -exec strip --strip-unneeded {} \; 2>/dev/null || true
    fi
    
    # Create tmpfs mount points
    for dir in $TMPFS_DIRS; do
        mkdir -p "$rootfs_dir$dir"
    done
    
    log_success "Filesystem optimized"
}

# Create SquashFS image
create_squashfs() {
    local rootfs_dir="$SQUASHFS_DIR/rootfs"
    local squashfs_file="$OUTPUT_DIR/filesystem-$ARCH.squashfs"
    
    log_info "Creating SquashFS image..."
    
    mkdir -p "$OUTPUT_DIR"
    
    # Create SquashFS with optimal compression
    mksquashfs "$rootfs_dir" "$squashfs_file" \
        -comp "$SQUASHFS_COMPRESSION" \
        -b "$SQUASHFS_BLOCK_SIZE" \
        $SQUASHFS_COMP_OPTS \
        -no-xattrs \
        -all-root
    
    # Check size constraint
    local size_mb=$(du -m "$squashfs_file" | cut -f1)
    if [ "$size_mb" -gt "$MAX_COMPRESSED_SIZE_MB" ]; then
        log_warning "SquashFS size ($size_mb MB) exceeds limit ($MAX_COMPRESSED_SIZE_MB MB)"
    fi
    
    log_success "SquashFS image created: $squashfs_file ($size_mb MB)"
}

# Main function
main() {
    log_info "Building SquashFS filesystem for $ARCH..."
    
    create_alpine_rootfs
    configure_repositories
    install_packages
    configure_system
    create_thin_user
    install_custom_files
    configure_services
    optimize_filesystem
    create_squashfs
    
    log_success "SquashFS build completed for $ARCH"
}

# Run main function
main "$@"
