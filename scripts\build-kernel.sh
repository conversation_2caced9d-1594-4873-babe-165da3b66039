#!/bin/bash
# ThinqAlpine Kernel Build Script
# Builds optimized kernel and initramfs for thin client

set -euo pipefail

# Script arguments
ARCH="${1:-x86_64}"
KERNEL_VERSION="${2:-6.1}"
KERNEL_DIR="${3:-build/kernel}"
OUTPUT_DIR="${4:-build/output}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Use Alpine's pre-built kernel
use_alpine_kernel() {
    local chroot_dir="$PROJECT_ROOT/build/alpine-$ARCH"
    local kernel_file="$OUTPUT_DIR/vmlinuz-$ARCH"
    local initrd_file="$OUTPUT_DIR/initrd-$ARCH"
    
    log_info "Using Alpine pre-built kernel for $ARCH..."
    
    if [ ! -d "$chroot_dir" ]; then
        log_error "Alpine chroot not found. Run setup-build-env.sh first."
        return 1
    fi
    
    # Find kernel in chroot
    local vmlinuz_path
    vmlinuz_path=$(find "$chroot_dir/boot" -name "vmlinuz-*" -type f | head -1)
    
    if [ -z "$vmlinuz_path" ]; then
        log_error "No kernel found in Alpine chroot"
        return 1
    fi
    
    # Copy kernel
    mkdir -p "$OUTPUT_DIR"
    cp "$vmlinuz_path" "$kernel_file"
    
    log_success "Kernel copied: $kernel_file"
    
    # Build custom initramfs
    build_initramfs "$chroot_dir" "$initrd_file"
    
    log_success "Alpine kernel setup completed"
}

# Build custom initramfs
build_initramfs() {
    local chroot_dir="$1"
    local initrd_file="$2"
    
    log_info "Building custom initramfs..."
    
    # Create initramfs working directory
    local initramfs_work="$KERNEL_DIR/initramfs"
    rm -rf "$initramfs_work"
    mkdir -p "$initramfs_work"
    
    # Create initramfs structure
    create_initramfs_structure "$initramfs_work"
    
    # Add essential binaries
    add_initramfs_binaries "$chroot_dir" "$initramfs_work"
    
    # Add kernel modules
    add_initramfs_modules "$chroot_dir" "$initramfs_work"
    
    # Create init script
    create_init_script "$initramfs_work"
    
    # Build initramfs archive
    cd "$initramfs_work"
    find . | cpio -o -H newc | gzip > "$initrd_file"
    
    log_success "Initramfs built: $initrd_file"
}

# Create initramfs directory structure
create_initramfs_structure() {
    local initramfs_work="$1"
    
    log_info "Creating initramfs structure..."
    
    # Create standard directories
    mkdir -p "$initramfs_work"/{bin,sbin,etc,proc,sys,dev,run,tmp,var,usr/{bin,sbin},lib,lib64}
    mkdir -p "$initramfs_work"/newroot
    mkdir -p "$initramfs_work"/mnt/{squashfs,overlay}
    
    # Create device nodes
    mknod "$initramfs_work/dev/console" c 5 1
    mknod "$initramfs_work/dev/null" c 1 3
    mknod "$initramfs_work/dev/zero" c 1 5
    mknod "$initramfs_work/dev/random" c 1 8
    mknod "$initramfs_work/dev/urandom" c 1 9
    
    log_success "Initramfs structure created"
}

# Add essential binaries to initramfs
add_initramfs_binaries() {
    local chroot_dir="$1"
    local initramfs_work="$2"
    
    log_info "Adding essential binaries to initramfs..."
    
    # Essential binaries
    local binaries=(
        "/bin/busybox"
        "/sbin/modprobe"
        "/bin/mount"
        "/bin/umount"
        "/bin/mkdir"
        "/bin/rmdir"
        "/bin/cp"
        "/bin/mv"
        "/bin/rm"
        "/bin/ln"
        "/bin/cat"
        "/bin/echo"
        "/bin/sleep"
        "/bin/wget"
        "/bin/sh"
    )
    
    for binary in "${binaries[@]}"; do
        if [ -f "$chroot_dir$binary" ]; then
            cp "$chroot_dir$binary" "$initramfs_work$binary"
            
            # Copy library dependencies
            copy_library_deps "$chroot_dir" "$initramfs_work" "$binary"
        fi
    done
    
    # Create busybox symlinks
    chroot "$initramfs_work" /bin/busybox --install -s
    
    log_success "Essential binaries added"
}

# Copy library dependencies
copy_library_deps() {
    local chroot_dir="$1"
    local initramfs_work="$2"
    local binary="$3"
    
    # Get library dependencies
    local libs
    libs=$(chroot "$chroot_dir" ldd "$binary" 2>/dev/null | grep -o '/[^ ]*' | sort -u || true)
    
    for lib in $libs; do
        if [ -f "$chroot_dir$lib" ] && [ ! -f "$initramfs_work$lib" ]; then
            mkdir -p "$initramfs_work$(dirname "$lib")"
            cp "$chroot_dir$lib" "$initramfs_work$lib"
        fi
    done
}

# Add kernel modules to initramfs
add_initramfs_modules() {
    local chroot_dir="$1"
    local initramfs_work="$2"
    
    log_info "Adding kernel modules to initramfs..."
    
    # Find kernel version
    local kernel_ver
    kernel_ver=$(ls "$chroot_dir/lib/modules/" | head -1)
    
    if [ -z "$kernel_ver" ]; then
        log_warning "No kernel modules found"
        return 0
    fi
    
    # Essential modules for thin client
    local modules=(
        "loop"
        "squashfs"
        "overlay"
        "bridge"
        "virtio"
        "virtio_net"
        "virtio_blk"
        "virtio_scsi"
        "virtio_pci"
        "e1000"
        "e1000e"
        "r8169"
    )
    
    # Create modules directory
    mkdir -p "$initramfs_work/lib/modules/$kernel_ver"
    
    # Copy essential modules
    for module in "${modules[@]}"; do
        find "$chroot_dir/lib/modules/$kernel_ver" -name "${module}.ko*" -exec cp {} "$initramfs_work/lib/modules/$kernel_ver/" \; 2>/dev/null || true
    done
    
    # Copy modules.dep and related files
    cp "$chroot_dir/lib/modules/$kernel_ver/modules."* "$initramfs_work/lib/modules/$kernel_ver/" 2>/dev/null || true
    
    log_success "Kernel modules added"
}

# Create init script for initramfs
create_init_script() {
    local initramfs_work="$1"
    
    log_info "Creating init script..."
    
    cat > "$initramfs_work/init" << 'EOF'
#!/bin/sh
# ThinqAlpine initramfs init script

# Mount essential filesystems
mount -t proc proc /proc
mount -t sysfs sysfs /sys
mount -t devtmpfs devtmpfs /dev

# Parse kernel command line
CMDLINE=$(cat /proc/cmdline)

# Extract squashfs URL
SQUASHFS_URL=""
for param in $CMDLINE; do
    case $param in
        squashfs_url=*)
            SQUASHFS_URL=${param#squashfs_url=}
            ;;
    esac
done

echo "ThinqAlpine initramfs starting..."
echo "SquashFS URL: $SQUASHFS_URL"

# Load essential modules
modprobe loop
modprobe squashfs
modprobe overlay

# Download SquashFS if URL provided
if [ -n "$SQUASHFS_URL" ]; then
    echo "Downloading SquashFS from $SQUASHFS_URL..."
    wget -O /tmp/filesystem.squashfs "$SQUASHFS_URL" || {
        echo "Failed to download SquashFS"
        exec /bin/sh
    }
    SQUASHFS_FILE="/tmp/filesystem.squashfs"
else
    echo "No SquashFS URL provided"
    exec /bin/sh
fi

# Mount SquashFS
echo "Mounting SquashFS..."
mount -t squashfs -o loop "$SQUASHFS_FILE" /mnt/squashfs || {
    echo "Failed to mount SquashFS"
    exec /bin/sh
}

# Setup overlay filesystem
echo "Setting up overlay filesystem..."
mkdir -p /mnt/overlay/{upper,work}
mount -t tmpfs tmpfs /mnt/overlay
mkdir -p /mnt/overlay/{upper,work}

mount -t overlay overlay \
    -o lowerdir=/mnt/squashfs,upperdir=/mnt/overlay/upper,workdir=/mnt/overlay/work \
    /newroot || {
    echo "Failed to setup overlay"
    exec /bin/sh
}

# Mount essential filesystems in new root
mount --bind /dev /newroot/dev
mount --bind /proc /newroot/proc
mount --bind /sys /newroot/sys

echo "Switching to new root..."

# Switch to new root
exec switch_root /newroot /sbin/init
EOF

    chmod +x "$initramfs_work/init"
    
    log_success "Init script created"
}

# Main function
main() {
    log_info "Building kernel for $ARCH..."
    
    mkdir -p "$KERNEL_DIR" "$OUTPUT_DIR"
    
    # Use Alpine's pre-built kernel (faster and more reliable)
    use_alpine_kernel
    
    log_success "Kernel build completed for $ARCH"
}

# Run main function
main "$@"
