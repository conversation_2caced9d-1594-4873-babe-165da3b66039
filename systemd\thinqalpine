#!/sbin/openrc-run
# ThinqAlpine OpenRC Service
# Manages the thin client QEMU startup process

name="ThinqAlpine Thin Client"
description="ThinqAlpine QEMU-based thin client service"

# Service configuration
command="/usr/bin/start-qemu.sh"
command_user="thin"
command_group="thin"
pidfile="/run/thinqalpine.pid"
command_background="yes"
start_stop_daemon_args="--make-pidfile --stdout /run/ThinqAlpine.log --stderr /run/ThinqAlpine.log"

# Dependencies
depend() {
    need net
    after networking dhcpcd
    use logger
}

# Pre-start setup
start_pre() {
    # Ensure log directory exists
    checkpath --directory --owner thin:thin --mode 0755 /run
    
    # Setup network bridge
    ebegin "Setting up network bridge"
    /usr/bin/setup-bridge.sh setup
    eend $?
    
    # Ensure thin user can access required devices
    if [ -e /dev/kvm ]; then
        chown root:kvm /dev/kvm
        chmod 660 /dev/kvm
        adduser thin kvm 2>/dev/null || true
    fi
    
    # Setup display environment for SDL
    export DISPLAY=:0
    export SDL_VIDEODRIVER=x11
    
    ebegin "Starting $name"
}

# Post-start validation
start_post() {
    # Wait a moment for the service to initialize
    sleep 2
    
    # Check if the process is running
    if ! pgrep -f "start-qemu.sh" > /dev/null; then
        eerror "$name failed to start properly"
        return 1
    fi
    
    einfo "$name started successfully"
}

# Pre-stop cleanup
stop_pre() {
    ebegin "Stopping $name"
}

# Post-stop cleanup
stop_post() {
    # Kill any remaining QEMU processes
    pkill -f qemu-system || true
    
    # Clean up bridge if needed
    /usr/bin/setup-bridge.sh cleanup 2>/dev/null || true
    
    eend $?
    einfo "$name stopped"
}

# Reload configuration
reload() {
    ebegin "Reloading $name configuration"
    
    # Send SIGUSR1 to reload (if supported by start-qemu.sh)
    if [ -f "$pidfile" ]; then
        kill -USR1 $(cat "$pidfile") 2>/dev/null || true
    fi
    
    eend $?
}

# Service status
status() {
    if [ -f "$pidfile" ] && kill -0 $(cat "$pidfile") 2>/dev/null; then
        einfo "$name is running (PID: $(cat "$pidfile"))"
        
        # Show additional status information
        if command -v pgrep >/dev/null; then
            local qemu_pids
            qemu_pids=$(pgrep -f qemu-system || true)
            if [ -n "$qemu_pids" ]; then
                einfo "QEMU processes: $qemu_pids"
            fi
        fi
        
        # Show bridge status
        /usr/bin/setup-bridge.sh status 2>/dev/null || true
        
        return 0
    else
        einfo "$name is not running"
        return 1
    fi
}
