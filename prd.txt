# Product Requirements Document (PRD)  
Project codename: “ThinqAlpine”  
Version: 1.0  
Date: 2025-09-01

---

## 1. Executive Summary
Build a **PXE-bootable, minimal Alpine-based Linux thin-client** that:
- Boots silently in pure text mode (no splash, no scrolling kernel messages).  
- Auto-logs in a single user account and launches QEMU in **SDL fullscreen** to run a second OS retrieved by iPXE.  
- Keeps the root filesystem in a **re-buildable SquashFS image** so administrators can add/remove packages later.  
- Accepts QEMU command-line arguments from iPXE kernel parameters.  
- Uses bridged networking so the guest OS can also PXE-boot from the same network.

---

## 2. Goals & Non-Goals

| Goals | Non-Goals |
|-------|-----------|
| ✅ < 150 MB compressed image | ❌ Persistent local storage |
| ✅ < 5 s from iPXE hand-off to QEMU window | ❌ Graphical Plymouth or X11 desktop |
| ✅ 100 % reproducible build via Alpine Live | ❌ Sound support |
| ✅ Re-signable SquashFS for field updates | ❌ BIOS/CSM support (UEFI only) |
| ✅ Works on both x86_64 and aarch64 | ❌ Dual-boot, encryption, or installer |

---

## 3. User Stories

1. **IT Admin**  
   “I run `make rebuild` on CI; it spits out a new initrd+SquashFS and my thin-clients pick it up at next reboot.”

2. **Field Technician**  
   “I append `qemu_cmd="-m 4096 -smp 4 -netdev user,id=n1"` to the iPXE script and the thin-client obeys without editing the image.”

3. **Security Auditor**  
   “No local attack surface—no SSH, no compilers, no writable root—only SDL+QEMU.”

---

## 4. Functional Requirements

| ID | Requirement |
|----|-------------|
| F1 | Image boots via UEFI PXE using **ipxe.efi** → **vmlinuz + initrd + filesystem.squashfs**. |
| F2 | Kernel suppresses ALL boot messages except a single line: `ThinqAlpine v1.0 <build-id>`. |
| F3 | Auto-login on tty1 as user **thin** with empty password. |
| F4 | Thin user’s shell is replaced by `/usr/bin/start-qemu.sh` which parses `/proc/cmdline` for key `qemu_cmd=...`. |
| F5 | QEMU launches with options: `-display sdl -full-screen -enable-kvm -netdev bridge,id=br0 -device virtio-net-pci,netdev=br0`. |
| F6 | Bridged network interface **br0** must exist; if not, script creates it with the first physical NIC as slave. |
| F7 | Poweroff/reboot triggered by `Ctrl-Alt-Del` (no other keyboard shortcuts exposed). |
| F8 | SquashFS can be re-created offline via `lb build` (Alpine Live toolchain) without touching initrd. |

---

## 5. Non-Functional Requirements

| ID | Metric |
|----|--------|
| N1 | Boot time ≤ 5 s from iPXE `boot` command to QEMU window visible. |
| N2 | Compressed image size ≤ 150 MB (initrd+filesystem.squashfs). |
| N3 | RAM footprint ≤ 256 MB before QEMU starts. |
| N4 | Reproducible build: same git commit + same Alpine snapshot → bit-identical SquashFS. |

---

## 6. Architecture Overview

```
┌──────────────┐                        ┌────────────────────┐
│ TFTP/NFS     │ PXE ──────┐            │ Build Server (CI)  │
│  (DHCP/HTTP) │          │             │  Alpine Live       │
└──────────────┘          ▼             └────────┬──────────┘
                                            SquashFS
                                                  │
┌─────────────────────────────────────────────────┼─────────────┐
│  Thin-Client RAM                               ▼             │
│  ┌────────────┐  ┌──────────────────┐  ┌──────────────┐   │
│  │   vmlinuz  │──┤  initrd (cpio)   │──┤ filesystem.sq│   │
│  └────────────┘  └──────────────────┘  └─────┬────────┘   │
│                                               │ OverlayFS  │
│  ┌──────────────────────────────────────────┐ │ (tmpfs)    │
│  │ QEMU (SDL-fullscreen)                    │ │            │
│  └──────────────────────────────────────────┘ │            │
└─────────────────────────────────────────────────────────────┘
```

---

## 7. Build & Release Process

1. **Toolchain**  
   - Alpine Live (`live-build`, `live-config`, `lb`)  
   - Custom package list in `config/package-lists/*.list.chroot`  
   - Kernel: Alpine-signed 6.10.x with minimal config (text, virtio, kvm).  

2. **Deploy**  
   - Copy artifacts to TFTP root and HTTP server path referenced by iPXE script.

---

## 8. iPXE Parameter Contract

Example iPXE snippet:

```
kernel http://pxe.example.com/thin/vmlinuz quiet loglevel=0 rd.systemd.show_status=false qemu_cmd="-display sdl -m 2048 -smp 2 initrd http://pxe.example.com/thin/initrd.img imgfetch http://pxe.example.com/thin/filesystem.squashfs
boot
```

Rules:
- If `qemu_cmd` is missing, script exits to emergency shell (fail fast).
- All arguments after the first non-option token in `qemu_cmd=` are passed verbatim to QEMU.

---

## 9. Security & Hardening

- Read-only rootfs; only `/run` and `/var/tmp` are tmpfs.
- No package manager, compiler, or interpreters in the image.

---

## 10. Logging & Diagnostics

- All systemd units redirect to `/dev/null` except `start-qemu.service` which writes to `/run/ThinqAlpine.log`.  

---

## 11. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| Network boot loop if DHCP fails | iPXE script sets `dhcp || reboot` |
| QEMU crash leaves black screen | `start-qemu.sh` has `while true; do qemu-system-x86_64 ... || sleep 1; done` |

---

## 12. Future Enhancements (out of scope for v1)

- HTTP(S) boot instead of TFTP.  
- Integrated VNC fallback when SDL unavailable.  

---

