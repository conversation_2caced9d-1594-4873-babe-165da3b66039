#!/bin/bash
# ThinqAlpine Build System Setup Script
# Prepares Ubuntu or Alpine build systems for ThinqAlpine development

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Detect host OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        OS_VERSION=$VERSION_ID
    else
        log_error "Cannot detect operating system"
        exit 1
    fi
    
    log_info "Detected OS: $OS $OS_VERSION"
}

# Check if running as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_error "This script should not be run as root"
        log_info "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check RAM
    local ram_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$ram_gb" -lt 8 ]; then
        log_warning "System has ${ram_gb}GB RAM, recommended minimum is 8GB"
    fi
    
    # Check disk space
    local disk_gb=$(df -BG "$PROJECT_ROOT" | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$disk_gb" -lt 20 ]; then
        log_warning "Available disk space is ${disk_gb}GB, recommended minimum is 20GB"
    fi
    
    # Check internet connectivity
    if ! ping -c 1 google.com &> /dev/null; then
        log_error "No internet connectivity detected"
        exit 1
    fi
    
    log_success "System requirements check completed"
}

# Install packages for Ubuntu/Debian
install_ubuntu_packages() {
    log_info "Installing packages for Ubuntu/Debian..."
    
    # Update package list
    sudo apt-get update
    
    # Install build dependencies
    sudo apt-get install -y \
        build-essential \
        git \
        wget \
        curl \
        rsync \
        squashfs-tools \
        genisoimage \
        syslinux-utils \
        isolinux \
        xorriso \
        dosfstools \
        mtools \
        bc \
        kmod \
        cpio \
        flex \
        bison \
        libelf-dev \
        libssl-dev \
        python3 \
        python3-pip \
        debootstrap \
        qemu-system-x86 \
        qemu-utils \
        bridge-utils \
        docker.io \
        docker-compose
    
    # Install iPXE build dependencies
    sudo apt-get install -y \
        liblzma-dev \
        mkisofs \
        mtools \
        perl \
        syslinux
    
    # Add user to docker group
    sudo usermod -aG docker "$USER"
    
    log_success "Ubuntu packages installed successfully"
}

# Install packages for Alpine Linux
install_alpine_packages() {
    log_info "Installing packages for Alpine Linux..."
    
    # Update package index
    sudo apk update
    
    # Install build dependencies
    sudo apk add \
        alpine-sdk \
        build-base \
        git \
        wget \
        curl \
        rsync \
        squashfs-tools \
        syslinux \
        xorriso \
        dosfstools \
        mtools \
        bc \
        linux-headers \
        python3 \
        py3-pip \
        qemu-system-x86_64 \
        qemu-img \
        bridge-utils \
        docker \
        docker-compose
    
    # Install iPXE build dependencies
    sudo apk add \
        xz-dev \
        perl \
        mtools
    
    # Add user to docker group
    sudo addgroup "$USER" docker
    
    # Enable and start docker
    sudo rc-update add docker boot
    sudo service docker start
    
    log_success "Alpine packages installed successfully"
}

# Setup build environment
setup_build_env() {
    log_info "Setting up build environment..."
    
    # Create build directories
    mkdir -p "$PROJECT_ROOT/build"
    mkdir -p "$PROJECT_ROOT/cache"
    mkdir -p "$PROJECT_ROOT/logs"
    
    # Setup ccache if enabled
    if [ "$CCACHE_ENABLE" = "true" ]; then
        mkdir -p "$CCACHE_DIR"
        export PATH="/usr/lib/ccache:$PATH"
        log_info "ccache enabled at $CCACHE_DIR"
    fi
    
    # Create build user if needed
    if ! id "$BUILD_USER" &>/dev/null; then
        sudo useradd -m -u "$BUILD_UID" -s /bin/bash "$BUILD_USER" || true
    fi
    
    log_success "Build environment setup completed"
}

# Setup Docker environment
setup_docker() {
    log_info "Setting up Docker environment..."
    
    # Check if Docker is running
    if ! docker info &>/dev/null; then
        log_error "Docker is not running. Please start Docker service."
        exit 1
    fi
    
    # Build Alpine builder image
    cat > "$PROJECT_ROOT/Dockerfile.builder" << 'EOF'
FROM alpine:3.18

RUN apk add --no-cache \
    alpine-sdk \
    build-base \
    git \
    wget \
    curl \
    rsync \
    squashfs-tools \
    syslinux \
    xorriso \
    dosfstools \
    mtools \
    bc \
    linux-headers \
    python3 \
    py3-pip \
    qemu-system-x86_64 \
    qemu-img \
    bridge-utils \
    xz-dev \
    perl \
    mtools

RUN adduser -D -u 1000 builder
USER builder
WORKDIR /workspace
EOF
    
    # Build the image
    docker build -t "$DOCKER_TAG" -f "$PROJECT_ROOT/Dockerfile.builder" "$PROJECT_ROOT"
    
    log_success "Docker environment setup completed"
}

# Validate installation
validate_installation() {
    log_info "Validating installation..."
    
    # Check required commands
    local required_commands=(
        "git" "wget" "curl" "rsync" "mksquashfs" 
        "mkisofs" "syslinux" "qemu-system-x86_64" "docker"
    )
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command '$cmd' not found"
            exit 1
        fi
    done
    
    # Test Docker
    if ! docker run --rm hello-world &>/dev/null; then
        log_warning "Docker test failed. You may need to log out and back in."
    fi
    
    log_success "Installation validation completed"
}

# Main function
main() {
    log_info "Starting ThinqAlpine build system setup..."
    
    check_root
    detect_os
    check_requirements
    
    case "$OS" in
        ubuntu|debian)
            install_ubuntu_packages
            ;;
        alpine)
            install_alpine_packages
            ;;
        *)
            log_error "Unsupported operating system: $OS"
            log_info "Supported systems: Ubuntu, Debian, Alpine Linux"
            exit 1
            ;;
    esac
    
    setup_build_env
    setup_docker
    validate_installation
    
    log_success "ThinqAlpine build system setup completed successfully!"
    log_info "You may need to log out and back in for group changes to take effect."
    log_info "Run 'make build' to start building ThinqAlpine."
}

# Run main function
main "$@"
