# ThinqAlpine Installation Guide

This guide walks you through setting up the ThinqAlpine build system on Ubuntu or Alpine Linux.

## System Requirements

### Minimum Requirements
- **OS**: Ubuntu 20.04+ or Alpine Linux 3.15+
- **RAM**: 8GB (16GB recommended)
- **Storage**: 20GB free space (50GB recommended)
- **CPU**: 4 cores (8 cores recommended)
- **Network**: Internet connection for package downloads

### Supported Architectures
- x86_64 (primary)
- aarch64 (experimental)

## Quick Installation

### 1. Clone or Extract ThinqAlpine
```bash
# If using git
git clone <repository-url> ThinqAlpine
cd ThinqAlpine

# If using tarball
tar -xzf thinqalpine-build-system.tar.gz
cd ThinqAlpine
```

### 2. Run Setup Script
```bash
# Make setup script executable
chmod +x scripts/setup-build-system.sh

# Run setup (will prompt for sudo password)
./scripts/setup-build-system.sh
```

### 3. Build ThinqAlpine
```bash
# Build for x86_64 (default)
make build

# Or build for specific architecture
make build ARCH=aarch64
```

### 4. Deploy to PXE Server
```bash
# Deploy to local TFTP/HTTP servers
sudo make deploy TFTP_ROOT=/var/lib/tftpboot HTTP_ROOT=/var/www/html

# Or deploy to remote servers
sudo make deploy TFTP_ROOT=/mnt/tftp-server HTTP_ROOT=/mnt/http-server
```

## Detailed Installation Steps

### Ubuntu/Debian Installation

1. **Update system packages**:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. **Run ThinqAlpine setup**:
   ```bash
   ./scripts/setup-build-system.sh
   ```

3. **Log out and back in** (required for Docker group membership):
   ```bash
   # Log out and back in, or run:
   newgrp docker
   ```

4. **Verify installation**:
   ```bash
   docker run --rm hello-world
   make version
   ```

### Alpine Linux Installation

1. **Enable community repository**:
   ```bash
   sudo nano /etc/apk/repositories
   # Uncomment the community line
   ```

2. **Update package index**:
   ```bash
   sudo apk update
   ```

3. **Run ThinqAlpine setup**:
   ```bash
   ./scripts/setup-build-system.sh
   ```

4. **Start Docker service**:
   ```bash
   sudo rc-service docker start
   sudo rc-update add docker boot
   ```

5. **Add user to docker group**:
   ```bash
   sudo addgroup $USER docker
   # Log out and back in
   ```

## Configuration

### Build Configuration
Edit `config/build.conf` to customize:

```bash
# Target architecture
ARCH=x86_64

# Alpine version
ALPINE_VERSION=3.18

# Kernel version
KERNEL_VERSION=6.1

# Size limits (PRD requirements)
MAX_COMPRESSED_SIZE_MB=150
MAX_RAM_FOOTPRINT_MB=256

# QEMU defaults
QEMU_DEFAULT_MEMORY=2048
QEMU_DEFAULT_CPUS=2
```

### Package Selection
Edit `alpine-config/packages.list` to add/remove packages:

```bash
# Add custom packages
nano alpine-config/packages.list

# Example additions:
# firefox
# chromium
# remmina
# freerdp
```

### Network Configuration
Edit bridge settings in `config/build.conf`:

```bash
# Bridge configuration
BRIDGE_NAME=br0
AUTO_CREATE_BRIDGE=true
```

## Verification

### Test Build
```bash
# Run comprehensive tests
make test

# Check specific components
make test ARCH=x86_64
```

### Validate Deployment
```bash
# Check deployed files
ls -la /var/lib/tftpboot/
ls -la /var/www/html/thinq/

# Test TFTP access
echo "quit" | tftp localhost -c get undionly.kpxe /tmp/test.kpxe

# Test HTTP access
curl -I http://localhost/thinq/version.json
```

## Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```bash
# Fix script permissions
find scripts -name "*.sh" -exec chmod +x {} \;

# Fix Docker permissions
sudo usermod -aG docker $USER
# Log out and back in
```

#### 2. Docker Not Running
```bash
# Ubuntu/Debian
sudo systemctl start docker
sudo systemctl enable docker

# Alpine
sudo rc-service docker start
sudo rc-update add docker boot
```

#### 3. Insufficient Disk Space
```bash
# Clean Docker images
docker system prune -a

# Clean build artifacts
make clean

# Check disk usage
df -h
du -sh build/
```

#### 4. Network Issues
```bash
# Test internet connectivity
ping -c 3 google.com

# Test Alpine mirror access
wget -O /dev/null http://dl-cdn.alpinelinux.org/alpine/v3.18/main/x86_64/APKINDEX.tar.gz

# Check DNS resolution
nslookup dl-cdn.alpinelinux.org
```

#### 5. Build Failures
```bash
# Check build logs
tail -f logs/build.log

# Rebuild specific components
make clean
make kernel
make squashfs
make ipxe
```

### Log Files
- Build logs: `logs/build.log`
- Test reports: `test-report.txt`
- Deployment summary: `deployment-summary.txt`

### Getting Help
1. Check the troubleshooting section in `docs/TROUBLESHOOTING.md`
2. Review build logs for specific error messages
3. Ensure all system requirements are met
4. Verify network connectivity and permissions

## Next Steps

After successful installation:

1. **Configure PXE Server**: See `docs/PXE-SETUP.md`
2. **Customize Build**: See `docs/CUSTOMIZATION.md`
3. **Deploy to Production**: See `docs/DEPLOYMENT.md`
4. **Monitor and Maintain**: See `docs/MAINTENANCE.md`

## Security Considerations

- Run builds in isolated environments
- Validate all downloaded packages
- Use secure networks for package downloads
- Regularly update base Alpine images
- Review custom package additions

## Performance Optimization

- Use SSD storage for build directory
- Enable ccache for faster rebuilds
- Use local Alpine mirrors for faster downloads
- Allocate sufficient RAM for parallel builds
- Consider using faster network connections
