#!/bin/bash
# Thinq<PERSON>lpine Test Script
# Validates built images and performs integration tests

set -euo pipefail

# Script arguments
OUTPUT_DIR="${1:-build/output}"
ARCH="${2:-x86_64}"

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Load configuration
source "$PROJECT_ROOT/config/build.conf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Test helper functions
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    log_info "Running test: $test_name"
    
    if eval "$test_command"; then
        log_success "PASS: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "FAIL: $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test file existence
test_file_exists() {
    local file="$1"
    [ -f "$file" ]
}

# Test file size constraints
test_file_size() {
    local file="$1"
    local max_size_mb="$2"
    
    if [ ! -f "$file" ]; then
        return 1
    fi
    
    local size_mb=$(du -m "$file" | cut -f1)
    [ "$size_mb" -le "$max_size_mb" ]
}

# Test SquashFS integrity
test_squashfs_integrity() {
    local squashfs_file="$1"
    
    if [ ! -f "$squashfs_file" ]; then
        return 1
    fi
    
    # Test mounting SquashFS
    local mount_point="/tmp/thinq-test-$$"
    mkdir -p "$mount_point"
    
    if mount -t squashfs -o loop "$squashfs_file" "$mount_point" 2>/dev/null; then
        # Check essential files exist
        local essential_files=(
            "$mount_point/bin/busybox"
            "$mount_point/usr/bin/qemu-system-x86_64"
            "$mount_point/usr/bin/start-qemu.sh"
            "$mount_point/etc/passwd"
        )
        
        local all_exist=true
        for file in "${essential_files[@]}"; do
            if [ ! -e "$file" ]; then
                all_exist=false
                break
            fi
        done
        
        umount "$mount_point"
        rmdir "$mount_point"
        
        $all_exist
    else
        rmdir "$mount_point" 2>/dev/null || true
        return 1
    fi
}

# Test kernel boot parameters
test_kernel_boot_params() {
    local kernel_file="$1"
    
    if [ ! -f "$kernel_file" ]; then
        return 1
    fi
    
    # Check if kernel is a valid Linux kernel
    file "$kernel_file" | grep -q "Linux kernel"
}

# Test iPXE script syntax
test_ipxe_script_syntax() {
    local ipxe_script="$PROJECT_ROOT/ipxe/thinqalpine.ipxe"
    
    if [ ! -f "$ipxe_script" ]; then
        return 1
    fi
    
    # Basic syntax checks
    grep -q "#!ipxe" "$ipxe_script" && \
    grep -q "dhcp" "$ipxe_script" && \
    grep -q "kernel" "$ipxe_script" && \
    grep -q "initrd" "$ipxe_script" && \
    grep -q "boot" "$ipxe_script"
}

# Test QEMU startup script
test_qemu_script() {
    local qemu_script="$PROJECT_ROOT/scripts/start-qemu.sh"
    
    if [ ! -f "$qemu_script" ]; then
        return 1
    fi
    
    # Check script syntax
    bash -n "$qemu_script" && \
    grep -q "parse_qemu_cmd" "$qemu_script" && \
    grep -q "setup_bridge" "$qemu_script" && \
    grep -q "launch_qemu" "$qemu_script"
}

# Test network bridge script
test_bridge_script() {
    local bridge_script="$PROJECT_ROOT/scripts/setup-bridge.sh"
    
    if [ ! -f "$bridge_script" ]; then
        return 1
    fi
    
    # Check script syntax
    bash -n "$bridge_script" && \
    grep -q "setup_bridge" "$bridge_script" && \
    grep -q "validate_bridge" "$bridge_script"
}

# Test total compressed size (PRD requirement N2)
test_total_size_constraint() {
    local total_size=0
    
    # Calculate total size of deployment files
    local files=(
        "$OUTPUT_DIR/vmlinuz-$ARCH"
        "$OUTPUT_DIR/initrd-$ARCH"
        "$OUTPUT_DIR/filesystem-$ARCH.squashfs"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file")
            total_size=$((total_size + size))
        fi
    done
    
    # Convert to MB
    local total_mb=$((total_size / 1024 / 1024))
    
    log_info "Total compressed size: ${total_mb}MB (limit: ${MAX_COMPRESSED_SIZE_MB}MB)"
    
    [ "$total_mb" -le "$MAX_COMPRESSED_SIZE_MB" ]
}

# Test configuration files
test_configuration_files() {
    local config_files=(
        "$PROJECT_ROOT/config/build.conf"
        "$PROJECT_ROOT/alpine-config/alpine-build.conf"
        "$PROJECT_ROOT/alpine-config/packages.list"
    )
    
    for config_file in "${config_files[@]}"; do
        if [ ! -f "$config_file" ]; then
            return 1
        fi
    done
    
    return 0
}

# Performance test (simulated)
test_boot_time_estimate() {
    # This is a simulated test - actual boot time testing requires hardware
    local initrd_size_mb=$(du -m "$OUTPUT_DIR/initrd-$ARCH" | cut -f1)
    local squashfs_size_mb=$(du -m "$OUTPUT_DIR/filesystem-$ARCH.squashfs" | cut -f1)
    
    # Rough estimate: 1MB/s download + 0.5s per MB decompression
    local estimated_time=$((initrd_size_mb + squashfs_size_mb + squashfs_size_mb / 2))
    
    log_info "Estimated boot time: ${estimated_time}s (target: ${TARGET_BOOT_TIME_SEC}s)"
    
    # This is just an estimate, so we'll be lenient
    [ "$estimated_time" -le $((TARGET_BOOT_TIME_SEC * 2)) ]
}

# Run all tests
run_all_tests() {
    log_info "Starting ThinqAlpine test suite for $ARCH..."
    
    # File existence tests
    run_test "Kernel file exists" "test_file_exists '$OUTPUT_DIR/vmlinuz-$ARCH'"
    run_test "Initrd file exists" "test_file_exists '$OUTPUT_DIR/initrd-$ARCH'"
    run_test "SquashFS file exists" "test_file_exists '$OUTPUT_DIR/filesystem-$ARCH.squashfs'"
    run_test "iPXE BIOS file exists" "test_file_exists '$OUTPUT_DIR/undionly.kpxe'"
    
    case "$ARCH" in
        x86_64)
            run_test "iPXE UEFI file exists" "test_file_exists '$OUTPUT_DIR/ipxe-x86_64.efi'"
            ;;
        aarch64)
            run_test "iPXE UEFI file exists" "test_file_exists '$OUTPUT_DIR/ipxe-aarch64.efi'"
            ;;
    esac
    
    # Size constraint tests
    run_test "SquashFS size constraint" "test_file_size '$OUTPUT_DIR/filesystem-$ARCH.squashfs' 120"
    run_test "Total size constraint" "test_total_size_constraint"
    
    # Integrity tests
    run_test "SquashFS integrity" "test_squashfs_integrity '$OUTPUT_DIR/filesystem-$ARCH.squashfs'"
    run_test "Kernel validity" "test_kernel_boot_params '$OUTPUT_DIR/vmlinuz-$ARCH'"
    
    # Script tests
    run_test "iPXE script syntax" "test_ipxe_script_syntax"
    run_test "QEMU script validity" "test_qemu_script"
    run_test "Bridge script validity" "test_bridge_script"
    run_test "Configuration files" "test_configuration_files"
    
    # Performance tests
    run_test "Boot time estimate" "test_boot_time_estimate"
}

# Generate test report
generate_test_report() {
    local report_file="$PROJECT_ROOT/test-report.txt"
    
    cat > "$report_file" << EOF
ThinqAlpine Test Report
======================

Test Date: $(date)
Architecture: $ARCH
Version: $THINQALPINE_VERSION
Build ID: $BUILD_ID

Test Results:
- Total Tests: $TESTS_TOTAL
- Passed: $TESTS_PASSED
- Failed: $TESTS_FAILED
- Success Rate: $(( (TESTS_PASSED * 100) / TESTS_TOTAL ))%

File Sizes:
$(ls -lh "$OUTPUT_DIR"/ 2>/dev/null || echo "Output directory not found")

System Information:
- OS: $(uname -s)
- Kernel: $(uname -r)
- Architecture: $(uname -m)
- Memory: $(free -h | grep '^Mem:' | awk '{print $2}')
- Disk Space: $(df -h "$PROJECT_ROOT" | tail -1 | awk '{print $4}') available

Build Configuration:
- Alpine Version: $ALPINE_VERSION
- Kernel Version: $KERNEL_VERSION
- Max Compressed Size: ${MAX_COMPRESSED_SIZE_MB}MB
- Target Boot Time: ${TARGET_BOOT_TIME_SEC}s

EOF
    
    if [ "$TESTS_FAILED" -eq 0 ]; then
        echo "Status: ALL TESTS PASSED ✓" >> "$report_file"
    else
        echo "Status: $TESTS_FAILED TESTS FAILED ✗" >> "$report_file"
    fi
    
    log_info "Test report saved to: $report_file"
}

# Main function
main() {
    log_info "ThinqAlpine Test Suite"
    log_info "Output Directory: $OUTPUT_DIR"
    log_info "Architecture: $ARCH"
    
    if [ ! -d "$OUTPUT_DIR" ]; then
        log_error "Output directory not found: $OUTPUT_DIR"
        log_info "Run 'make build' first to create the images"
        exit 1
    fi
    
    run_all_tests
    generate_test_report
    
    echo ""
    echo "========================================="
    echo "Test Summary"
    echo "========================================="
    echo "Total Tests: $TESTS_TOTAL"
    echo "Passed: $TESTS_PASSED"
    echo "Failed: $TESTS_FAILED"
    echo "Success Rate: $(( (TESTS_PASSED * 100) / TESTS_TOTAL ))%"
    echo "========================================="
    
    if [ "$TESTS_FAILED" -eq 0 ]; then
        log_success "All tests passed! ThinqAlpine is ready for deployment."
        exit 0
    else
        log_error "$TESTS_FAILED tests failed. Please review and fix issues before deployment."
        exit 1
    fi
}

# Run main function
main "$@"
