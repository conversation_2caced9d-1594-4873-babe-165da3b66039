#!/bin/bash
# ThinqAlpine Network Bridge Setup Script
# Creates and configures bridge network for QEMU guest networking

set -euo pipefail

# Configuration
BRIDGE_NAME="${1:-br0}"
LOG_FILE="/run/ThinqAlpine.log"

# Logging functions
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$$] $1" | tee -a "$LOG_FILE"
}

log_error() {
    log "ERROR: $1"
}

log_info() {
    log "INFO: $1"
}

log_warning() {
    log "WARNING: $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

# Find physical network interfaces
find_physical_interfaces() {
    ip link show | grep -E '^[0-9]+: (eth|ens|enp|wlan)[0-9]' | cut -d: -f2 | tr -d ' ' | grep -v '^lo$'
}

# Get interface IP configuration
get_interface_config() {
    local interface="$1"
    local config=""
    
    # Get IP address
    local ip_addr
    ip_addr=$(ip addr show "$interface" | grep 'inet ' | awk '{print $2}' | head -1)
    
    if [ -n "$ip_addr" ]; then
        config="static:$ip_addr"
        
        # Get gateway
        local gateway
        gateway=$(ip route show default | grep "dev $interface" | awk '{print $3}' | head -1)
        if [ -n "$gateway" ]; then
            config="$config:$gateway"
        fi
    else
        config="dhcp"
    fi
    
    echo "$config"
}

# Setup bridge interface
setup_bridge() {
    local bridge_name="$1"
    local physical_if="$2"
    local config="$3"
    
    log_info "Setting up bridge $bridge_name with interface $physical_if"
    
    # Check if bridge already exists
    if ip link show "$bridge_name" &>/dev/null; then
        log_info "Bridge $bridge_name already exists"
        return 0
    fi
    
    # Save current interface configuration
    local old_config
    old_config=$(get_interface_config "$physical_if")
    
    # Bring down physical interface
    ip link set dev "$physical_if" down
    
    # Remove IP from physical interface
    ip addr flush dev "$physical_if"
    
    # Create bridge
    ip link add name "$bridge_name" type bridge
    ip link set dev "$bridge_name" up
    
    # Add physical interface to bridge
    ip link set dev "$physical_if" master "$bridge_name"
    ip link set dev "$physical_if" up
    
    # Configure bridge networking
    case "$config" in
        dhcp)
            log_info "Configuring bridge with DHCP"
            dhcpcd "$bridge_name" &
            ;;
        static:*)
            local ip_info
            ip_info=$(echo "$config" | cut -d: -f2-)
            local ip_addr
            ip_addr=$(echo "$ip_info" | cut -d: -f1)
            local gateway
            gateway=$(echo "$ip_info" | cut -d: -f2)
            
            log_info "Configuring bridge with static IP: $ip_addr"
            ip addr add "$ip_addr" dev "$bridge_name"
            
            if [ -n "$gateway" ] && [ "$gateway" != "$ip_addr" ]; then
                ip route add default via "$gateway" dev "$bridge_name"
            fi
            ;;
    esac
    
    # Enable IP forwarding for bridge
    echo 1 > /proc/sys/net/ipv4/ip_forward
    
    # Setup iptables rules for NAT (if needed)
    setup_iptables "$bridge_name"
    
    log_info "Bridge $bridge_name setup completed"
}

# Setup iptables rules for bridge networking
setup_iptables() {
    local bridge_name="$1"
    
    log_info "Setting up iptables rules for bridge networking"
    
    # Allow bridge traffic
    iptables -I FORWARD -i "$bridge_name" -j ACCEPT 2>/dev/null || true
    iptables -I FORWARD -o "$bridge_name" -j ACCEPT 2>/dev/null || true
    
    # Allow bridge to bridge communication
    iptables -I FORWARD -i "$bridge_name" -o "$bridge_name" -j ACCEPT 2>/dev/null || true
    
    log_info "iptables rules configured"
}

# Cleanup bridge
cleanup_bridge() {
    local bridge_name="$1"
    
    log_info "Cleaning up bridge $bridge_name"
    
    if ip link show "$bridge_name" &>/dev/null; then
        # Get bridge members
        local members
        members=$(ls "/sys/class/net/$bridge_name/brif/" 2>/dev/null || true)
        
        # Remove members from bridge
        for member in $members; do
            log_info "Removing $member from bridge"
            ip link set dev "$member" nomaster
        done
        
        # Delete bridge
        ip link delete "$bridge_name" type bridge
        
        log_info "Bridge $bridge_name removed"
    fi
}

# Validate bridge setup
validate_bridge() {
    local bridge_name="$1"
    
    log_info "Validating bridge setup..."
    
    # Check if bridge exists
    if ! ip link show "$bridge_name" &>/dev/null; then
        log_error "Bridge $bridge_name does not exist"
        return 1
    fi
    
    # Check if bridge is up
    if ! ip link show "$bridge_name" | grep -q "state UP"; then
        log_error "Bridge $bridge_name is not up"
        return 1
    fi
    
    # Check if bridge has members
    local member_count
    member_count=$(ls "/sys/class/net/$bridge_name/brif/" 2>/dev/null | wc -l)
    if [ "$member_count" -eq 0 ]; then
        log_warning "Bridge $bridge_name has no member interfaces"
    fi
    
    # Test connectivity (ping gateway if available)
    local gateway
    gateway=$(ip route show default | grep "dev $bridge_name" | awk '{print $3}' | head -1)
    if [ -n "$gateway" ]; then
        if ping -c 1 -W 2 "$gateway" &>/dev/null; then
            log_info "Bridge connectivity test passed"
        else
            log_warning "Bridge connectivity test failed"
        fi
    fi
    
    log_info "Bridge validation completed"
    return 0
}

# Show bridge status
show_bridge_status() {
    local bridge_name="$1"
    
    echo "=== Bridge Status: $bridge_name ==="
    
    if ip link show "$bridge_name" &>/dev/null; then
        echo "Bridge exists: YES"
        echo "Bridge state: $(ip link show "$bridge_name" | grep -o 'state [A-Z]*' | cut -d' ' -f2)"
        
        # Show IP configuration
        local ip_addr
        ip_addr=$(ip addr show "$bridge_name" | grep 'inet ' | awk '{print $2}' | head -1)
        echo "IP address: ${ip_addr:-none}"
        
        # Show members
        echo "Bridge members:"
        local members
        members=$(ls "/sys/class/net/$bridge_name/brif/" 2>/dev/null || true)
        if [ -n "$members" ]; then
            for member in $members; do
                echo "  - $member"
            done
        else
            echo "  (none)"
        fi
        
        # Show routes
        echo "Routes via bridge:"
        ip route show dev "$bridge_name" | sed 's/^/  /'
    else
        echo "Bridge exists: NO"
    fi
    
    echo "=========================="
}

# Main function
main() {
    local action="${1:-setup}"
    local bridge_name="${2:-$BRIDGE_NAME}"
    
    # Setup logging
    mkdir -p "$(dirname "$LOG_FILE")"
    
    case "$action" in
        setup)
            check_root
            
            # Find physical interfaces
            local interfaces
            interfaces=$(find_physical_interfaces)
            
            if [ -z "$interfaces" ]; then
                log_error "No physical network interfaces found"
                exit 1
            fi
            
            # Use first available interface
            local physical_if
            physical_if=$(echo "$interfaces" | head -1)
            
            # Get current configuration
            local config
            config=$(get_interface_config "$physical_if")
            
            # Setup bridge
            setup_bridge "$bridge_name" "$physical_if" "$config"
            
            # Validate setup
            validate_bridge "$bridge_name"
            ;;
            
        cleanup)
            check_root
            cleanup_bridge "$bridge_name"
            ;;
            
        status)
            show_bridge_status "$bridge_name"
            ;;
            
        validate)
            validate_bridge "$bridge_name"
            ;;
            
        *)
            echo "Usage: $0 {setup|cleanup|status|validate} [bridge_name]"
            echo ""
            echo "Commands:"
            echo "  setup    - Create and configure bridge (default)"
            echo "  cleanup  - Remove bridge and restore interfaces"
            echo "  status   - Show bridge status"
            echo "  validate - Validate bridge configuration"
            echo ""
            echo "Default bridge name: $BRIDGE_NAME"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
